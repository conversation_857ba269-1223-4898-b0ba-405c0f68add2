{"compilerOptions": {"types": ["node"], "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "#/*": ["src/api/*"], "$/*": ["src/stores/*"]}, "allowJs": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/views/ledger/components/lib/worker.js", "src/worker/worker.js"], "references": [{"path": "./tsconfig.node.json"}]}