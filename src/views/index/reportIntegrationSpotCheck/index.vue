<script setup lang="ts" name="reportIntegrationSpotCheck">
import ExcelJS from 'exceljs'
import { useUserStore } from '@/stores/useUserStore'
import { useRouter } from 'vue-router'

// 路由实例
const router = useRouter()

// 任务数据类型定义
interface TaskData {
  id: string
  index: number
  taskId: string
  taskName: string
  category: string
  dataSource: string
  priority: string
  progress: number
  status: string
  hasException: boolean
  createTime: string
  startTime: string
  estimatedRemainingTime: string // 预计剩余时间
}
// 搜索表单
const searchFormProp = ref([
  { label: '任务名称', prop: 'taskName', type: 'text' },
  { 
    label: '分类', 
    prop: 'category', 
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '财政类', value: '财政类' },
      { label: '医保类', value: '医保类' },
      { label: '土地类', value: '土地类' },
      { label: '教育类', value: '教育类' }
    ]
  },
  { 
    label: '状态', 
    prop: 'status', 
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '进行中', value: '进行中' },
      { label: '已暂停', value: '已暂停' },
      { label: '已超时', value: '已超时' },
      { label: '已完成', value: '已完成' }
    ]
  }
])
const searchForm = ref({ taskName: '', category: '', status: '' })

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref(null)

// 表头配置
const columns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'taskId', label: '抽查任务ID', minWidth: 120 },
  { prop: 'taskName', label: '抽查任务名称', minWidth: 200 },
  { prop: 'category', label: '分类', minWidth: 100 },
  { prop: 'dataSource', label: '数据来源', minWidth: 150 },
  { prop: 'priority', label: '优先级', minWidth: 100, sortable: true },
  { prop: 'progress', label: '进程', minWidth: 150 },
  { prop: 'estimatedRemainingTime', label: '预计剩余时间', minWidth: 120 },
  { prop: 'status', label: '状态', minWidth: 100 },
  { prop: 'action', label: '操作', width: 200, fixed: 'right' }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  taskName: '',
  category: '',
  status: '',
  skipCount: 0,
  maxResultCount: 10
})

// 表格数据
const tableData = ref<TaskData[]>([])
const selectedRows = ref<TaskData[]>([])

// 排序状态
const sortConfig = ref({
  prop: '',
  order: '' // 'ascending' | 'descending' | ''
})

// 日志弹窗相关
const showLogDialog = ref(false)
const currentTaskLog = ref<any[]>([])
const currentTaskName = ref('')
const currentTaskId = ref('')
const logSearchKeyword = ref('')
const logPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})
const allTaskLogs = ref<any[]>([]) // 存储所有日志数据

// 日志表格列配置
const logColumns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'operationType', label: '操作类型', width: 120 },
  { prop: 'operationTime', label: '操作时间', width: 180 },
  { prop: 'operator', label: '操作人员', width: 120 },
  { prop: 'operationDetail', label: '操作详情/备注', minWidth: 200 }
]

// 数据备份相关
const showDataBackupDialog = ref(false)
const dataBackupTableData = ref<any[]>([])
const dataBackupPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})
const dataBackupSearchForm = ref({
  backupName: '',
  startTime: '',
  endTime: ''
})
const dataBackupSelectedRows = ref<any[]>([])
const dataBackupLoading = ref(false)

// 数据备份表格列配置
const dataBackupColumns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'backupName', label: '备份名称', minWidth: 200 },
  { prop: 'backupTime', label: '备份时间', width: 180 },
  { prop: 'timeRange', label: '时间范围', minWidth: 200 },
  { prop: 'dataSize', label: '数据大小', width: 120 },
  { prop: 'status', label: '状态', width: 100 },
  { prop: 'operator', label: '操作人员', width: 120 },
  { prop: 'action', label: '操作', width: 200, fixed: 'right' }
]

// 新增/修改数据备份弹窗
const showDataBackupFormDialog = ref(false)
const dataBackupFormRef = ref()
const dataBackupForm = ref<any>({})
const editingDataBackupIndex = ref(-1)

// 数据备份表单属性
const dataBackupFormProps = ref([
  { label: '备份名称', prop: 'backupName', type: 'text', placeholder: '请输入备份名称' },
  { label: '开始时间', prop: 'startTime', type: 'datetime' },
  { label: '结束时间', prop: 'endTime', type: 'datetime' },
  { label: '备份说明', prop: 'description', type: 'textarea', placeholder: '请输入备份说明（可选）' }
])

// 数据备份表单校验规则
const dataBackupFormRules = {
  backupName: [{ required: true, message: '请输入备份名称', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && dataBackupForm.value.startTime && new Date(value) <= new Date(dataBackupForm.value.startTime)) {
          callback(new Error('结束时间必须大于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 对话框相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<any>({})

// 查看任务配置相关
const showViewTaskDialog = ref(false)
const viewTaskForm = ref<any>({})
const viewTaskDataSources = ref<any[]>([])

// 数据源配置相关
const showDataSourceDialog = ref(false)
const dataSourceFormRef = ref()
const dataSourceForm = ref<any>({})
const currentDataSources = ref<any[]>([])
const editingDataSourceIndex = ref(-1)

// 弹窗表单属性
const dialogFormProps = ref([
  { label: '任务名称', prop: 'taskName', type: 'text' },
  { 
    label: '任务分类', 
    prop: 'category', 
    type: 'select',
    options: [
      { label: '财政类', value: '财政类' },
      { label: '医保类', value: '医保类' },
      { label: '土地类', value: '土地类' },
      { label: '教育类', value: '教育类' }
    ]
  },
  { label: '启动时间', prop: 'startTime', type: 'datetime' },
  { 
    label: '任务时限', 
    prop: 'timeLimit', 
    type: 'select',
    options: [
      { label: '小于0.5小时', value: '小于0.5小时' },
      { label: '0.5-1小时', value: '0.5-1小时' },
      { label: '1-2小时', value: '1-2小时' },
      { label: '2-4小时', value: '2-4小时' },
      { label: '4-8小时', value: '4-8小时' },
      { label: '大于8小时', value: '大于8小时' }
    ]
  },
  { 
    label: '优先级', 
    prop: 'priority', 
    type: 'radio',
    options: [
      { label: '低', value: '低' },
      { label: '中', value: '中' },
      { label: '高', value: '高' }
    ]
  },
  { 
    label: '选择模板', 
    prop: 'template', 
    type: 'select',
    options: [
      { label: '模板1', value: '模板1' },
      { label: '模板2', value: '模板2' },
      { label: '模板3', value: '模板3' }
    ]
  }
])

// 数据源配置表单属性
const dataSourceFormProps = ref([
  {
    label: '算法选择',
    prop: 'algorithm',
    type: 'select',
    options: [
      { label: '算法1', value: '算法1' },
      { label: '算法2', value: '算法2' },
      { label: '算法3', value: '算法3' },
      { label: '深度学习算法', value: '深度学习算法' },
      { label: '机器学习算法', value: '机器学习算法' }
    ]
  },
  {
    label: '数据获取源配置',
    prop: 'dataSource',
    type: 'select',
    options: [
      { label: '数据源1', value: '数据源1' },
      { label: '数据源2', value: '数据源2' },
      { label: '数据源3', value: '数据源3' },
      { label: '财政数据源', value: '财政数据源' },
      { label: '医保数据源', value: '医保数据源' }
    ]
  },
  { label: '服务器地址', prop: 'serverAddress', type: 'text', placeholder: '请输入服务器IP或域名' },
  { label: '端口', prop: 'port', type: 'text', placeholder: '请输入端口号' },
  { label: '开始时间', prop: 'startTime', type: 'datetime' },
  { label: '结束时间', prop: 'endTime', type: 'datetime' },
  {
    label: '获取数据类型',
    prop: 'dataTypes',
    type: 'select',
    multiple: true,
    options: [
      { label: '日报', value: '日报' },
      { label: '周报', value: '周报' },
      { label: '月报', value: '月报' },
      { label: '季报', value: '季报' },
      { label: '年报', value: '年报' },
      { label: '实时数据', value: '实时数据' }
    ]
  },
  { label: '获取数据量', prop: 'dataVolume', type: 'text', placeholder: '请输入数据量大小' }
])

// 弹框表单校验规则
const dialogFormRules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择任务分类', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择启动时间', trigger: 'change' }],
  timeLimit: [{ required: true, message: '请选择任务时限', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  template: [{ required: true, message: '请选择模板', trigger: 'change' }]
}

// 数据源配置表单校验规则
const dataSourceFormRules = {
  algorithm: [{ required: true, message: '请选择算法', trigger: 'change' }],
  dataSource: [{ required: true, message: '请选择数据获取源', trigger: 'change' }],
  serverAddress: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    {
      pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/,
      message: '请输入正确的IP地址或域名格式',
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback()
          return
        }
        const port = parseInt(value)
        if (isNaN(port) || port < 1 || port > 65535) {
          callback(new Error('端口号必须是1-65535之间的数字'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  dataTypes: [{ required: true, message: '请选择获取数据类型', trigger: 'change' }],
  dataVolume: [
    { required: true, message: '请输入获取数据量', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback()
          return
        }
        const volume = parseInt(value)
        if (isNaN(volume) || volume < 1) {
          callback(new Error('数据量必须是大于0的数字'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取当前用户信息
const userStore = useUserStore()
const getCurrentOperatorName = (): string => {
  const userInfo = userStore.getUserInfo
  if (userInfo) {
    return userInfo.displayName || userInfo.name || userInfo.account || '未知用户'
  }
  return '未知用户'
}

// 添加操作日志记录
const addOperationLog = (taskId: string, operationType: string, operationDetail: string) => {
  const taskLogs = JSON.parse(localStorage.getItem('taskOperationLogs') || '{}')

  if (!taskLogs[taskId]) {
    taskLogs[taskId] = []
  }

  const newLog = {
    index: taskLogs[taskId].length + 1,
    operationType,
    operationTime: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }),
    operator: getCurrentOperatorName(),
    operationDetail
  }

  // 添加到日志列表开头（最新的在前）
  taskLogs[taskId].unshift(newLog)

  // 限制日志数量，最多保留50条
  if (taskLogs[taskId].length > 50) {
    taskLogs[taskId] = taskLogs[taskId].slice(0, 50)
  }

  // 重新编号
  taskLogs[taskId].forEach((log: any, index: number) => {
    log.index = index + 1
  })

  localStorage.setItem('taskOperationLogs', JSON.stringify(taskLogs))
}

// 生成任务日志数据
const generateTaskLogs = (taskId: string) => {
  // 从localStorage获取实际的操作记录
  const taskLogs = JSON.parse(localStorage.getItem('taskOperationLogs') || '{}')
  const actualLogs = taskLogs[taskId] || []

  // 如果没有实际操作记录，生成一些模拟的历史记录
  if (actualLogs.length === 0) {
    const operationTypes = ['创建', '查看', '导出', '恢复', '暂停']
    const operators = ['张三', '李四', '王五', '赵六', '钱七', '孙八']

    const details = {
      '创建': '任务创建成功，开始执行数据抽查',
      '查看': '查看任务执行详情和配置信息',
      '导出': '导出任务执行报告',
      '恢复': '系统维护完成，恢复任务执行',
      '暂停': '由于系统维护暂停任务'
    }

    const logs = []
    const logCount = Math.floor(Math.random() * 5) + 3 // 3-7条历史日志

    // 生成一些历史操作记录
    for (let i = 0; i < logCount; i++) {
      const operationType = operationTypes[Math.floor(Math.random() * operationTypes.length)]
      const baseTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      const operationTime = new Date(baseTime.getTime() - i * 2 * 60 * 60 * 1000)

      logs.push({
        index: i + 1,
        operationType,
        operationTime: operationTime.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        operator: operators[Math.floor(Math.random() * operators.length)],
        operationDetail: details[operationType as keyof typeof details]
      })
    }

    // 保存生成的历史记录
    taskLogs[taskId] = logs.sort((a, b) => new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime())
    localStorage.setItem('taskOperationLogs', JSON.stringify(taskLogs))

    return taskLogs[taskId]
  }

  // 返回实际的操作记录（已经按时间倒序排列）
  return actualLogs
}

// 生成预计剩余时间
const generateEstimatedRemainingTime = (status: string, progress: number) => {
  switch (status) {
    case '已完成':
      return '0小时'
    case '已超时':
      return '已超时'
    case '已暂停':
      // 暂停状态显示暂停前的剩余时间
      const pausedHours = Math.floor(Math.random() * 24) + 1
      return `${pausedHours}小时(暂停中)`
    case '进行中':
      // 根据进度计算剩余时间，进度越高剩余时间越少
      const remainingProgress = 100 - progress
      const estimatedHours = Math.floor((remainingProgress / 100) * 48) + 1 // 1-48小时
      return `${estimatedHours}小时`
    default:
      return '未知'
  }
}

// 生成模拟数据
const generateMockData = () => {
  const categories = ['财政类', '医保类', '土地类', '教育类']
  const priorities = ['低', '中', '高']
  const statuses = ['进行中', '已暂停', '已超时', '已完成']
  const dataSources = ['数据源一', '数据源二', '数据源三', '数据源四', '数据源五']

  const data = []
  for (let i = 1; i <= 50; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const progress = status === '已完成' ? 100 :
                    status === '进行中' ? Math.floor(Math.random() * 90) + 10 :
                    status === '已暂停' ? Math.floor(Math.random() * 60) + 10 :
                    Math.floor(Math.random() * 80) + 10

    const hasException = Math.random() < 0.15 // 15%概率有异常
    const finalStatus = hasException && status === '进行中' ? (Math.random() < 0.5 ? '已暂停' : '已超时') : status

    data.push({
      id: `task_${i}`,
      index: i,
      taskId: `00158942${String(i).padStart(2, '0')}`,
      taskName: `任务${i}`,
      category: categories[Math.floor(Math.random() * categories.length)],
      dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      progress: progress,
      status: finalStatus,
      hasException: hasException,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      startTime: new Date().toISOString(),
      estimatedRemainingTime: generateEstimatedRemainingTime(finalStatus, progress)
    })
  }
  return data
}

// 初始化数据
const initData = () => {
  const savedData = localStorage.getItem('reportIntegrationSpotCheckData')
  if (savedData) {
    tableData.value = JSON.parse(savedData)
  } else {
    tableData.value = generateMockData()
    localStorage.setItem('reportIntegrationSpotCheckData', JSON.stringify(tableData.value))
  }
  pagination.total = tableData.value.length
}

// 获取过滤后的数据
const getFilteredData = () => {
  let filtered = [...tableData.value]
  
  if (reqParams.taskName) {
    filtered = filtered.filter(item => 
      item.taskName.includes(reqParams.taskName) || 
      item.taskId.includes(reqParams.taskName)
    )
  }
  
  if (reqParams.category) {
    filtered = filtered.filter(item => item.category === reqParams.category)
  }
  
  if (reqParams.status) {
    filtered = filtered.filter(item => item.status === reqParams.status)
  }
  
  return filtered
}

// 获取当前页数据
const getCurrentPageData = () => {
  let filtered = getFilteredData()

  // 应用排序
  if (sortConfig.value.prop && sortConfig.value.order) {
    filtered = getSortedData(filtered)
  }

  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  pagination.total = filtered.length
  return filtered.slice(start, end)
}

// 获取排序后的数据
const getSortedData = (data: TaskData[]) => {
  if (!sortConfig.value.prop || !sortConfig.value.order) {
    return data
  }

  const { prop, order } = sortConfig.value

  return [...data].sort((a, b) => {
    if (prop === 'priority') {
      // 优先级排序：高 > 中 > 低
      const priorityOrder = { '高': 3, '中': 2, '低': 1 }
      const aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0
      const bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0

      if (order === 'ascending') {
        return aValue - bValue
      } else {
        return bValue - aValue
      }
    }

    return 0
  })
}

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortConfig.value.prop = prop
  sortConfig.value.order = order

  // 重新加载数据
  tableRef.value?.reload()
}

// 查询
const onSearch = async () => {
  try {
    searchLoading.value = true

    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    pagination.page = 1
    reqParams.skipCount = 0
    reqParams.maxResultCount = pagination.size
    reqParams.taskName = searchForm.value.taskName
    reqParams.category = searchForm.value.category
    reqParams.status = searchForm.value.status

    tableRef.value?.reload()
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searchLoading.value = false
  }
}

// 动态生成操作按钮
const getButtons = (row: any) => {
  const baseButtons = [
    { label: '查看', type: 'primary', code: 'view' },
    { label: '日志', type: 'info', code: 'log' }
  ]

  if (row.status === '进行中') {
    baseButtons.push({ label: '暂停', type: 'warning', code: 'pause' })
  } else if (row.status === '已暂停') {
    baseButtons.push({ label: '恢复', type: 'success', code: 'resume' })
  }

  // 只有非进行中状态才显示删除按钮
  if (row.status !== '进行中') {
    baseButtons.push({ label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' })
  }

  return baseButtons
}

// 查看任务配置
const viewTaskConfig = (row: any) => {
  // 记录查看操作日志
  addOperationLog(row.taskId, '查看', `查看任务"${row.taskName}"的配置信息`)

  // 加载任务基本信息
  viewTaskForm.value = {
    taskName: row.taskName,
    category: row.category,
    startTime: row.startTime,
    timeLimit: '1-2小时', // 模拟数据
    priority: row.priority,
    template: '模板1' // 模拟数据
  }

  // 加载任务关联的数据源配置
  const taskDataSources = JSON.parse(localStorage.getItem('taskDataSources') || '{}')
  viewTaskDataSources.value = taskDataSources[row.id] || []

  showViewTaskDialog.value = true
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'view':
      viewTaskConfig(row)
      break
    case 'log':
      showTaskLog(row)
      break
    case 'pause':
      pauseTask(row)
      break
    case 'resume':
      resumeTask(row)
      break
    case 'delete':
      deleteTask(row)
      break
  }
}

// 获取过滤后的日志数据
const getFilteredLogs = () => {
  if (!logSearchKeyword.value) {
    return allTaskLogs.value
  }

  return allTaskLogs.value.filter(log =>
    log.operator.includes(logSearchKeyword.value) ||
    log.operationType.includes(logSearchKeyword.value) ||
    log.operationDetail.includes(logSearchKeyword.value)
  )
}

// 获取当前页日志数据
const getCurrentPageLogs = () => {
  const filtered = getFilteredLogs()
  const start = (logPagination.page - 1) * logPagination.size
  const end = start + logPagination.size
  logPagination.total = filtered.length
  return filtered.slice(start, end)
}

// 日志搜索
const onLogSearch = () => {
  logPagination.page = 1
  currentTaskLog.value = getCurrentPageLogs()
}

// 日志分页变化
const onLogPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    logPagination.page = val
  } else {
    logPagination.size = val
    logPagination.page = 1
  }
  currentTaskLog.value = getCurrentPageLogs()
}

// 显示任务日志
const showTaskLog = (row: any) => {
  currentTaskName.value = row.taskName
  currentTaskId.value = row.taskId
  logSearchKeyword.value = ''
  logPagination.page = 1
  logPagination.size = 10

  // 生成所有日志数据
  allTaskLogs.value = generateTaskLogs(row.taskId)

  // 获取当前页数据
  currentTaskLog.value = getCurrentPageLogs()

  showLogDialog.value = true
}

// 暂停任务
const pauseTask = (row: any) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    tableData.value[index].status = '已暂停'
    localStorage.setItem('reportIntegrationSpotCheckData', JSON.stringify(tableData.value))

    // 记录暂停操作日志
    addOperationLog(row.taskId, '暂停', `暂停任务"${row.taskName}"的执行`)

    ElMessage.success('任务已暂停')
    tableRef.value?.reload()
  }
}

// 恢复任务
const resumeTask = (row: any) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    tableData.value[index].status = '进行中'
    localStorage.setItem('reportIntegrationSpotCheckData', JSON.stringify(tableData.value))

    // 记录恢复操作日志
    addOperationLog(row.taskId, '恢复', `恢复任务"${row.taskName}"的执行`)

    ElMessage.success('任务已恢复')
    tableRef.value?.reload()
  }
}

// 删除任务
const deleteTask = (row: any) => {
  ElMessageBox.confirm(
    `确认删除任务"${row.taskName}"吗？删除后将无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      // 记录删除操作日志
      addOperationLog(row.taskId, '删除', `删除任务"${row.taskName}"`)

      tableData.value.splice(index, 1)
      localStorage.setItem('reportIntegrationSpotCheckData', JSON.stringify(tableData.value))
      ElMessage.success('删除成功')
      tableRef.value?.reload()
    }
  }).catch(() => {
    // 用户取消删除，不做任何操作
  })
}

// 新增任务
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    priority: '中',
    startTime: new Date().toISOString()
  }
  currentDataSources.value = []
  showDialogForm.value = true
}

// 添加数据源配置
const onAddDataSource = () => {
  editingDataSourceIndex.value = -1
  dataSourceForm.value = {
    algorithm: '',
    dataSource: '',
    serverAddress: '',
    port: '8080',
    startTime: '',
    endTime: '',
    dataTypes: [],
    dataVolume: '1000'
  }
  showDataSourceDialog.value = true
}

// 编辑数据源配置
const onEditDataSource = (index: number) => {
  editingDataSourceIndex.value = index
  dataSourceForm.value = { ...currentDataSources.value[index] }
  showDataSourceDialog.value = true
}

// 删除数据源配置
const onDeleteDataSource = (index: number) => {
  ElMessageBox.confirm(
    '确认删除此数据源配置吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    currentDataSources.value.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 数据源配置表单提交
const onDataSourceConfirm = () => {
  dataSourceFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const dataSourceConfig = {
        id: editingDataSourceIndex.value >= 0 ? currentDataSources.value[editingDataSourceIndex.value].id : `ds_${Date.now()}`,
        ...dataSourceForm.value
      }

      if (editingDataSourceIndex.value >= 0) {
        // 编辑模式
        currentDataSources.value[editingDataSourceIndex.value] = dataSourceConfig
        ElMessage.success('数据源配置更新成功')
      } else {
        // 新增模式
        currentDataSources.value.push(dataSourceConfig)
        ElMessage.success('数据源配置添加成功')
      }

      showDataSourceDialog.value = false
    }
  })
}

// 导出功能
const onExport = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的数据')
    return
  }

  try {
    loading.value = true

    // 记录导出操作日志（为每个选中的任务记录）
    selectedRows.value.forEach(row => {
      addOperationLog(row.taskId, '导出', `导出任务"${row.taskName}"的数据到Excel文件`)
    })

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Report Integration Spot Check System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('报表整合度抽查任务')

    // 设置列定义
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '抽查任务ID', key: 'taskId', width: 15 },
      { header: '抽查任务名称', key: 'taskName', width: 25 },
      { header: '优先级', key: 'priority', width: 12 },
      { header: '进程', key: 'progress', width: 12 },
      { header: '状态', key: 'status', width: 12 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' }

    // 添加数据
    selectedRows.value.forEach((row, index) => {
      worksheet.addRow({
        index: index + 1,
        taskId: row.taskId,
        taskName: row.taskName,
        priority: row.priority,
        progress: `${row.progress}%`,
        status: row.status
      })
    })

    // 设置数据行样式
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // 跳过表头
        row.alignment = { vertical: 'middle', horizontal: 'center' }
      }

      // 设置边框
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 生成文件名：YYYY-MM-DD-HH-mm-ss-报表整合度抽查任务表.xlsx
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-').replace('T', '-')
    const filename = `${timestamp}-报表整合度抽查任务表.xlsx`

    // 下载文件
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success(`导出成功: ${filename}`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    loading.value = false
  }
}

// 批量删除
const onBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }

  // 检查是否有进行中的任务
  const runningTasks = selectedRows.value.filter(row => row.status === '进行中')
  if (runningTasks.length > 0) {
    ElMessage.error('不能删除进行中的任务')
    return
  }

  ElMessageBox.confirm(
    `确认删除选中的 ${selectedRows.value.length} 条记录吗？`,
    '批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const selectedIds = selectedRows.value.map(row => row.id)
    tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))
    localStorage.setItem('reportIntegrationSpotCheckData', JSON.stringify(tableData.value))
    selectedRows.value = []
    ElMessage.success('批量删除成功')
    tableRef.value?.reload()
  })
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    reqParams.maxResultCount = pagination.size
    pagination.page = 1
  }
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 弹框表单提交
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true

      // 模拟保存延迟
      setTimeout(() => {
        const taskId = `task_${Date.now()}`
        const newTaskId = `00158942${String(tableData.value.length + 1).padStart(2, '0')}`
        const newTask = {
          id: taskId,
          index: tableData.value.length + 1,
          taskId: newTaskId,
          taskName: dialogForm.value.taskName,
          category: dialogForm.value.category,
          dataSource: currentDataSources.value.length > 0 ? currentDataSources.value[0].dataSource : '数据源一',
          priority: dialogForm.value.priority,
          progress: 0,
          status: '进行中',
          hasException: false,
          createTime: new Date().toISOString(),
          startTime: dialogForm.value.startTime,
          estimatedRemainingTime: generateEstimatedRemainingTime('进行中', 0)
        }

        tableData.value.unshift(newTask)
        localStorage.setItem('reportIntegrationSpotCheckData', JSON.stringify(tableData.value))

        // 保存任务关联的数据源配置
        if (currentDataSources.value.length > 0) {
          const taskDataSources = JSON.parse(localStorage.getItem('taskDataSources') || '{}')
          taskDataSources[taskId] = currentDataSources.value
          localStorage.setItem('taskDataSources', JSON.stringify(taskDataSources))
        }

        // 记录创建操作日志
        addOperationLog(newTaskId, '创建', `创建任务"${dialogForm.value.taskName}"，开始执行数据抽查`)

        ElMessage.success('创建任务成功')
        showDialogForm.value = false
        tableRef.value?.reload()
        loading.value = false
      }, 1000)
    }
  })
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '进行中':
      return 'primary'
    case '已暂停':
      return 'warning'
    case '已超时':
      return 'danger'
    case '已完成':
      return 'success'
    default:
      return 'info'
  }
}

// 获取操作类型颜色
const getOperationTypeColor = (operationType: string) => {
  switch (operationType) {
    case '创建':
      return 'success'
    case '修改':
      return 'warning'
    case '查看':
      return 'info'
    case '暂停':
      return 'warning'
    case '恢复':
      return 'success'
    case '删除':
      return 'danger'
    case '导出':
      return 'primary'
    default:
      return 'info'
  }
}

// 数据备份相关方法
// 生成数据备份模拟数据
const generateDataBackupMockData = () => {
  const backupNames = ['日常数据备份', '周度数据备份', '月度数据备份', '季度数据备份', '年度数据备份']
  const operators = ['张三', '李四', '王五', '赵六', '钱七']
  const statuses = ['备份中', '已完成', '备份失败']

  const data = []
  for (let i = 1; i <= 30; i++) {
    const backupTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    const startTime = new Date(backupTime.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000)
    const endTime = new Date(startTime.getTime() + Math.random() * 24 * 60 * 60 * 1000)
    const status = statuses[Math.floor(Math.random() * statuses.length)]

    data.push({
      id: `backup_${i}`,
      index: i,
      backupName: `${backupNames[Math.floor(Math.random() * backupNames.length)]}_${i}`,
      backupTime: backupTime.toLocaleString('zh-CN'),
      timeRange: `${startTime.toLocaleString('zh-CN')} ~ ${endTime.toLocaleString('zh-CN')}`,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      dataSize: `${(Math.random() * 1000 + 100).toFixed(2)}MB`,
      status: status,
      operator: operators[Math.floor(Math.random() * operators.length)],
      description: `备份说明${i}`
    })
  }
  return data.sort((a, b) => new Date(b.backupTime).getTime() - new Date(a.backupTime).getTime())
}

// 初始化数据备份数据
const initDataBackupData = () => {
  const savedData = localStorage.getItem('reportIntegrationDataBackup')
  if (savedData) {
    dataBackupTableData.value = JSON.parse(savedData)
  } else {
    dataBackupTableData.value = generateDataBackupMockData()
    localStorage.setItem('reportIntegrationDataBackup', JSON.stringify(dataBackupTableData.value))
  }
  dataBackupPagination.total = dataBackupTableData.value.length
}

// 获取过滤后的数据备份数据
const getFilteredDataBackupData = () => {
  let filtered = [...dataBackupTableData.value]

  if (dataBackupSearchForm.value.backupName) {
    filtered = filtered.filter(item =>
      item.backupName.includes(dataBackupSearchForm.value.backupName)
    )
  }

  if (dataBackupSearchForm.value.startTime) {
    filtered = filtered.filter(item =>
      new Date(item.backupTime) >= new Date(dataBackupSearchForm.value.startTime)
    )
  }

  if (dataBackupSearchForm.value.endTime) {
    filtered = filtered.filter(item =>
      new Date(item.backupTime) <= new Date(dataBackupSearchForm.value.endTime)
    )
  }

  return filtered
}

// 获取当前页数据备份数据
const getCurrentPageDataBackupData = () => {
  const filtered = getFilteredDataBackupData()
  const start = (dataBackupPagination.page - 1) * dataBackupPagination.size
  const end = start + dataBackupPagination.size
  dataBackupPagination.total = filtered.length
  return filtered.slice(start, end)
}

// 打开数据备份弹窗
const openDataBackupDialog = () => {
  initDataBackupData()
  showDataBackupDialog.value = true
}

// 数据备份搜索
const onDataBackupSearch = () => {
  dataBackupPagination.page = 1
}

// 数据备份分页变化
const onDataBackupPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    dataBackupPagination.page = val
  } else {
    dataBackupPagination.size = val
    dataBackupPagination.page = 1
  }
}

// 数据备份选择变化
const handleDataBackupSelectionChange = (selection: any[]) => {
  dataBackupSelectedRows.value = selection
}

// 新增数据备份
const onAddDataBackup = () => {
  editingDataBackupIndex.value = -1
  dataBackupForm.value = {
    backupName: '',
    startTime: '',
    endTime: '',
    description: ''
  }
  showDataBackupFormDialog.value = true
}

// 编辑数据备份
const onEditDataBackup = (row: any) => {
  const index = dataBackupTableData.value.findIndex(item => item.id === row.id)
  editingDataBackupIndex.value = index
  dataBackupForm.value = {
    backupName: row.backupName,
    startTime: row.startTime,
    endTime: row.endTime,
    description: row.description
  }
  showDataBackupFormDialog.value = true
}

// 删除数据备份
const onDeleteDataBackup = (row: any) => {
  ElMessageBox.confirm(
    `确认删除备份"${row.backupName}"吗？删除后将无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    const index = dataBackupTableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      dataBackupTableData.value.splice(index, 1)
      localStorage.setItem('reportIntegrationDataBackup', JSON.stringify(dataBackupTableData.value))
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 数据备份表单提交
const onDataBackupFormConfirm = () => {
  dataBackupFormRef.value.validate((valid: boolean) => {
    if (valid) {
      dataBackupLoading.value = true

      // 模拟保存延迟
      setTimeout(() => {
        if (editingDataBackupIndex.value >= 0) {
          // 编辑模式
          const item = dataBackupTableData.value[editingDataBackupIndex.value]
          item.backupName = dataBackupForm.value.backupName
          item.startTime = dataBackupForm.value.startTime
          item.endTime = dataBackupForm.value.endTime
          item.description = dataBackupForm.value.description
          item.timeRange = `${new Date(dataBackupForm.value.startTime).toLocaleString('zh-CN')} ~ ${new Date(dataBackupForm.value.endTime).toLocaleString('zh-CN')}`

          ElMessage.success('修改成功')
        } else {
          // 新增模式
          const newBackup = {
            id: `backup_${Date.now()}`,
            index: dataBackupTableData.value.length + 1,
            backupName: dataBackupForm.value.backupName,
            backupTime: new Date().toLocaleString('zh-CN'),
            timeRange: `${new Date(dataBackupForm.value.startTime).toLocaleString('zh-CN')} ~ ${new Date(dataBackupForm.value.endTime).toLocaleString('zh-CN')}`,
            startTime: dataBackupForm.value.startTime,
            endTime: dataBackupForm.value.endTime,
            dataSize: `${(Math.random() * 1000 + 100).toFixed(2)}MB`,
            status: '已完成',
            operator: getCurrentOperatorName(),
            description: dataBackupForm.value.description
          }

          dataBackupTableData.value.unshift(newBackup)
          ElMessage.success('创建成功')
        }

        localStorage.setItem('reportIntegrationDataBackup', JSON.stringify(dataBackupTableData.value))
        showDataBackupFormDialog.value = false
        dataBackupLoading.value = false
      }, 1000)
    }
  })
}

// 数据备份导出
const onDataBackupExport = async () => {
  if (dataBackupSelectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的数据')
    return
  }

  try {
    dataBackupLoading.value = true

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Report Integration Spot Check System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('数据备份记录')

    // 设置列定义
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '备份名称', key: 'backupName', width: 25 },
      { header: '备份时间', key: 'backupTime', width: 20 },
      { header: '时间范围', key: 'timeRange', width: 35 },
      { header: '数据大小', key: 'dataSize', width: 15 },
      { header: '状态', key: 'status', width: 12 },
      { header: '操作人员', key: 'operator', width: 15 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' }

    // 添加数据
    dataBackupSelectedRows.value.forEach((row, index) => {
      worksheet.addRow({
        index: index + 1,
        backupName: row.backupName,
        backupTime: row.backupTime,
        timeRange: row.timeRange,
        dataSize: row.dataSize,
        status: row.status,
        operator: row.operator
      })
    })

    // 设置数据行样式
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // 跳过表头
        row.alignment = { vertical: 'middle', horizontal: 'center' }
      }

      // 设置边框
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 生成文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-').replace('T', '-')
    const filename = `${timestamp}-数据备份记录.xlsx`

    // 下载文件
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success(`导出成功: ${filename}`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    dataBackupLoading.value = false
  }
}

// 获取数据备份状态类型
const getDataBackupStatusType = (status: string) => {
  switch (status) {
    case '备份中':
      return 'warning'
    case '已完成':
      return 'success'
    case '备份失败':
      return 'danger'
    default:
      return 'info'
  }
}

// 数据脱敏设置相关
const showDataMaskingDialog = ref(false)
const dataMaskingFormRef = ref()
const dataMaskingForm = ref<any>({})
const dataMaskingLoading = ref(false)
const testResult = ref('')

// 操作监控日志相关
const showOperationLogDialog = ref(false)
const operationLogTableData = ref<any[]>([])
const operationLogPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})
const operationLogSearchForm = ref({
  startTime: '',
  endTime: '',
  operationType: '',
  operator: ''
})
const operationLogSelectedRows = ref<any[]>([])
const operationLogLoading = ref(false)

// 操作监控日志表格列配置
const operationLogColumns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'taskId', label: '任务ID', width: 120 },
  { prop: 'taskName', label: '任务名称', minWidth: 150 },
  { prop: 'operationType', label: '操作类型', width: 120 },
  { prop: 'operationTime', label: '操作时间', width: 180 },
  { prop: 'operator', label: '操作人员', width: 120 },
  { prop: 'operationDetail', label: '操作详情/备注', minWidth: 200 }
]

// 异常操作监测设置相关
const showAbnormalMonitorDialog = ref(false)
const abnormalMonitorFormRef = ref()
const abnormalMonitorForm = ref<any>({})
const abnormalMonitorLoading = ref(false)

// 任务依赖关系设置相关
const showTaskDependencyDialog = ref(false)
const taskDependencyLoading = ref(false)
const taskDependencyTreeRef = ref()

// 任务依赖关系数据类型定义
interface TaskDependencyNode {
  id: string
  taskId: string
  taskName: string
  label: string
  category: string
  status: string
  children?: TaskDependencyNode[]
}

// 任务关系映射类型定义
interface TaskRelationship {
  taskId: string
  parentTaskId: string | null
}

// 任务依赖关系数据
const taskDependencyData = ref<TaskDependencyNode[]>([])

// 任务关系映射
const taskRelationships = ref<TaskRelationship[]>([])

// 可用的任务列表（从表格数据中获取）
const availableTasks = ref<TaskData[]>([])

// 当前选中的节点
const currentSelectedNode = ref<TaskDependencyNode | null>(null)

// 新增任务关系表单
const newTaskRelationForm = ref({
  selectedTaskId: '',
  parentTaskId: ''
})

// 异常操作监测表单校验规则
const abnormalMonitorFormRules = {
  userType: [{ required: true, message: '请选择用户类型', trigger: 'change' }],
  operationTypes: [
    { required: true, message: '请至少选择一种操作类型', trigger: 'change' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value || value.length === 0) {
          callback(new Error('请至少选择一种操作类型'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && abnormalMonitorForm.value.startTime && new Date(value) <= new Date(abnormalMonitorForm.value.startTime)) {
          callback(new Error('结束时间必须大于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  loginThreshold: [
    { required: true, message: '请输入登录次数阈值', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== undefined && value !== null && value !== '') {
          const num = parseInt(value)
          if (isNaN(num) || num < 1 || num > 1000) {
            callback(new Error('登录次数阈值必须是1-1000之间的整数'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  fileSizeLimit: [
    { required: true, message: '请输入文件大小限制', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== undefined && value !== null && value !== '') {
          const num = parseFloat(value)
          if (isNaN(num) || num < 0.1 || num > 10240) {
            callback(new Error('文件大小限制必须是0.1-10240之间的数字'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 数据脱敏表单属性
const dataMaskingFormProps = ref([
  { label: '脱敏规则名称', prop: 'ruleName', type: 'text', placeholder: '请输入' },
  {
    label: '字段类型',
    prop: 'fieldType',
    type: 'radio',
    options: [
      { label: '字符串', value: '字符串' },
      { label: '小数', value: '小数' },
      { label: '日期时间', value: '日期时间' }
    ]
  },
  {
    label: '脱敏算法',
    prop: 'algorithm',
    type: 'select',
    options: [
      { label: '保留前后位数算法', value: '保留前后位数算法' },
      { label: '完全替换算法', value: '完全替换算法' },
      { label: '部分显示算法', value: '部分显示算法' }
    ]
  }
])

// 数据脱敏表单校验规则
const dataMaskingFormRules = {
  ruleName: [{ required: true, message: '请输入脱敏规则名称', trigger: 'blur' }],
  fieldType: [{ required: true, message: '请选择字段类型', trigger: 'change' }],
  algorithm: [{ required: true, message: '请选择脱敏算法', trigger: 'change' }],
  frontKeep: [
    { required: true, message: '请输入保留前位数', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== undefined && value !== null && value !== '') {
          const num = parseInt(value)
          if (isNaN(num) || num < 0) {
            callback(new Error('请输入大于等于0的数字'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  backKeep: [
    { required: true, message: '请输入保留后位数', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== undefined && value !== null && value !== '') {
          const num = parseInt(value)
          if (isNaN(num) || num < 0) {
            callback(new Error('请输入大于等于0的数字'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 数据脱敏相关方法
// 打开数据脱敏设置弹窗
const openDataMaskingDialog = () => {
  // 从localStorage读取已保存的配置
  const savedConfig = localStorage.getItem('reportIntegrationDataMaskingConfig')
  if (savedConfig) {
    const config = JSON.parse(savedConfig)
    dataMaskingForm.value = {
      ruleName: config.ruleName || '',
      fieldType: config.fieldType || '字符串',
      algorithm: config.algorithm || '保留前后位数算法',
      frontKeep: config.frontKeep || 2,
      backKeep: config.backKeep || 2,
      testInput: ''
    }
  } else {
    dataMaskingForm.value = {
      ruleName: '',
      fieldType: '字符串',
      algorithm: '保留前后位数算法',
      frontKeep: 2,
      backKeep: 2,
      testInput: ''
    }
  }
  testResult.value = ''
  showDataMaskingDialog.value = true
}

// 脱敏算法实现
const applyDataMasking = (input: string, frontKeep: number, backKeep: number, fieldType: string) => {
  if (!input) return ''

  const inputStr = String(input)
  const totalLength = inputStr.length

  // 如果输入长度小于等于保留的总位数，则不进行脱敏
  if (totalLength <= frontKeep + backKeep) {
    return inputStr
  }

  // 根据字段类型进行不同的处理
  switch (fieldType) {
    case '字符串':
      return maskString(inputStr, frontKeep, backKeep)
    case '小数':
      return maskNumber(inputStr, frontKeep, backKeep)
    case '日期时间':
      return maskDateTime(inputStr, frontKeep, backKeep)
    default:
      return maskString(inputStr, frontKeep, backKeep)
  }
}

// 字符串脱敏
const maskString = (str: string, frontKeep: number, backKeep: number) => {
  const front = str.substring(0, frontKeep)
  const back = str.substring(str.length - backKeep)
  const middle = '*'.repeat(str.length - frontKeep - backKeep)
  return front + middle + back
}

// 数字脱敏
const maskNumber = (str: string, frontKeep: number, backKeep: number) => {
  // 对于数字，保持小数点的位置
  if (str.includes('.')) {
    const parts = str.split('.')
    const integerPart = parts[0]
    const decimalPart = parts[1]

    if (integerPart.length > frontKeep + backKeep) {
      const maskedInteger = maskString(integerPart, frontKeep, backKeep)
      return maskedInteger + '.' + decimalPart
    } else {
      return str
    }
  } else {
    return maskString(str, frontKeep, backKeep)
  }
}

// 日期时间脱敏
const maskDateTime = (str: string, frontKeep: number, backKeep: number) => {
  // 对于日期时间，保持格式字符的位置
  const formatChars = ['-', ':', ' ', '/', '.']
  let result = ''
  let charCount = 0
  let maskedCount = 0

  for (let i = 0; i < str.length; i++) {
    const char = str[i]

    if (formatChars.includes(char)) {
      result += char
    } else {
      charCount++
      if (charCount <= frontKeep || charCount > str.replace(/[-: \/.]/g, '').length - backKeep) {
        result += char
      } else {
        result += '*'
        maskedCount++
      }
    }
  }

  return result
}

// 测试脱敏效果
const testDataMasking = () => {
  const { testInput, frontKeep, backKeep, fieldType } = dataMaskingForm.value
  testResult.value = applyDataMasking(testInput || '', parseInt(frontKeep), parseInt(backKeep), fieldType)
}

// 数据脱敏配置表单提交
const onDataMaskingFormConfirm = () => {
  dataMaskingFormRef.value.validate((valid: boolean) => {
    if (valid) {
      dataMaskingLoading.value = true

      // 模拟保存延迟
      setTimeout(() => {
        const config = {
          ruleName: dataMaskingForm.value.ruleName,
          fieldType: dataMaskingForm.value.fieldType,
          algorithm: dataMaskingForm.value.algorithm,
          frontKeep: parseInt(dataMaskingForm.value.frontKeep),
          backKeep: parseInt(dataMaskingForm.value.backKeep),
          saveTime: new Date().toLocaleString('zh-CN')
        }

        localStorage.setItem('reportIntegrationDataMaskingConfig', JSON.stringify(config))
        ElMessage.success('数据脱敏配置保存成功')
        showDataMaskingDialog.value = false
        dataMaskingLoading.value = false
      }, 1000)
    }
  })
}

// 操作监控日志相关方法
// 打开操作监控日志弹窗
const openOperationLogDialog = () => {
  initOperationLogData()
  showOperationLogDialog.value = true
}

// 初始化操作监控日志数据
const initOperationLogData = () => {
  const allLogs = getAllOperationLogs()
  operationLogTableData.value = allLogs
  operationLogPagination.total = allLogs.length
}

// 获取所有操作日志
const getAllOperationLogs = () => {
  const taskLogs = JSON.parse(localStorage.getItem('taskOperationLogs') || '{}')
  const allLogs: any[] = []

  // 获取任务数据以便获取任务名称
  const tasks = JSON.parse(localStorage.getItem('reportIntegrationSpotCheckData') || '[]')
  const taskMap = new Map()
  tasks.forEach((task: any) => {
    taskMap.set(task.taskId, task.taskName)
  })

  // 遍历所有任务的日志
  Object.keys(taskLogs).forEach(taskId => {
    const logs = taskLogs[taskId] || []
    logs.forEach((log: any) => {
      allLogs.push({
        ...log,
        taskId: taskId,
        taskName: taskMap.get(taskId) || '未知任务'
      })
    })
  })

  // 按操作时间倒序排列
  return allLogs.sort((a, b) => new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime())
}

// 获取过滤后的操作日志数据
const getFilteredOperationLogData = () => {
  let filtered = [...operationLogTableData.value]

  if (operationLogSearchForm.value.startTime) {
    filtered = filtered.filter(item =>
      new Date(item.operationTime) >= new Date(operationLogSearchForm.value.startTime)
    )
  }

  if (operationLogSearchForm.value.endTime) {
    filtered = filtered.filter(item =>
      new Date(item.operationTime) <= new Date(operationLogSearchForm.value.endTime)
    )
  }

  if (operationLogSearchForm.value.operationType) {
    filtered = filtered.filter(item =>
      item.operationType.includes(operationLogSearchForm.value.operationType)
    )
  }

  if (operationLogSearchForm.value.operator) {
    filtered = filtered.filter(item =>
      item.operator.includes(operationLogSearchForm.value.operator)
    )
  }

  return filtered
}

// 获取当前页操作日志数据
const getCurrentPageOperationLogData = () => {
  const filtered = getFilteredOperationLogData()
  const start = (operationLogPagination.page - 1) * operationLogPagination.size
  const end = start + operationLogPagination.size
  operationLogPagination.total = filtered.length
  return filtered.slice(start, end)
}

// 操作日志搜索
const onOperationLogSearch = () => {
  operationLogPagination.page = 1
}

// 操作日志分页变化
const onOperationLogPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    operationLogPagination.page = val
  } else {
    operationLogPagination.size = val
    operationLogPagination.page = 1
  }
}

// 操作日志选择变化
const handleOperationLogSelectionChange = (selection: any[]) => {
  operationLogSelectedRows.value = selection
}

// 导出所有操作日志
const onExportAllOperationLogs = async () => {
  const allLogs = getFilteredOperationLogData()
  if (allLogs.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  await exportOperationLogs(allLogs, '所有操作日志')
}

// 导出选中的操作日志
const onExportSelectedOperationLogs = async () => {
  if (operationLogSelectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的数据')
    return
  }

  await exportOperationLogs(operationLogSelectedRows.value, '选中操作日志')
}

// 导出操作日志
const exportOperationLogs = async (logs: any[], type: string) => {
  try {
    operationLogLoading.value = true

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Report Integration Spot Check System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('操作监控日志')

    // 设置列定义
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '任务ID', key: 'taskId', width: 15 },
      { header: '任务名称', key: 'taskName', width: 25 },
      { header: '操作类型', key: 'operationType', width: 15 },
      { header: '操作时间', key: 'operationTime', width: 20 },
      { header: '操作人员', key: 'operator', width: 15 },
      { header: '操作详情/备注', key: 'operationDetail', width: 35 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' }

    // 添加数据
    logs.forEach((log, index) => {
      worksheet.addRow({
        index: index + 1,
        taskId: log.taskId,
        taskName: log.taskName,
        operationType: log.operationType,
        operationTime: log.operationTime,
        operator: log.operator,
        operationDetail: log.operationDetail
      })
    })

    // 设置数据行样式
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // 跳过表头
        row.alignment = { vertical: 'middle', horizontal: 'left' }
      }

      // 设置边框
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 生成文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-').replace('T', '-')
    const filename = `${timestamp}-${type}.xlsx`

    // 下载文件
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success(`导出成功: ${filename}`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    operationLogLoading.value = false
  }
}

// 异常操作监测相关方法
// 打开异常操作监测设置弹窗
const openAbnormalMonitorDialog = () => {
  // 从localStorage读取已保存的配置
  const savedConfigs = JSON.parse(localStorage.getItem('reportIntegrationAbnormalMonitorConfigs') || '[]')

  // 如果有保存的配置，使用最后一次保存的配置
  if (savedConfigs.length > 0) {
    // 获取最后一次保存的配置（按保存时间排序）
    const lastConfig = savedConfigs.sort((a: any, b: any) =>
      new Date(b.saveTime).getTime() - new Date(a.saveTime).getTime()
    )[0]

    abnormalMonitorForm.value = {
      userType: lastConfig.userType || '管理员',
      operationTypes: lastConfig.operationTypes || ['登录'],
      startTime: lastConfig.startTime || '',
      endTime: lastConfig.endTime || '',
      loginThreshold: lastConfig.loginThreshold || 10,
      fileSizeLimit: lastConfig.fileSizeLimit || 100
    }
  } else {
    // 初始化表单数据（首次使用时的默认值）
    abnormalMonitorForm.value = {
      userType: '管理员',
      operationTypes: ['登录'],
      startTime: '',
      endTime: '',
      loginThreshold: 10,
      fileSizeLimit: 100
    }
  }

  showAbnormalMonitorDialog.value = true
}

// 异常操作监测配置表单提交
const onAbnormalMonitorFormConfirm = () => {
  abnormalMonitorFormRef.value.validate((valid: boolean) => {
    if (valid) {
      abnormalMonitorLoading.value = true

      // 模拟保存延迟
      setTimeout(() => {
        const config = {
          id: `config_${Date.now()}`,
          userType: abnormalMonitorForm.value.userType,
          operationTypes: abnormalMonitorForm.value.operationTypes,
          startTime: abnormalMonitorForm.value.startTime,
          endTime: abnormalMonitorForm.value.endTime,
          loginThreshold: parseInt(abnormalMonitorForm.value.loginThreshold),
          fileSizeLimit: parseFloat(abnormalMonitorForm.value.fileSizeLimit),
          saveTime: new Date().toLocaleString('zh-CN'),
          isActive: true
        }

        // 获取现有配置
        const savedConfigs = JSON.parse(localStorage.getItem('reportIntegrationAbnormalMonitorConfigs') || '[]')

        // 检查是否已存在相同用户类型的配置
        const existingIndex = savedConfigs.findIndex((item: any) => item.userType === config.userType)

        if (existingIndex >= 0) {
          // 更新现有配置
          savedConfigs[existingIndex] = config
          ElMessage.success(`${config.userType}的异常操作监测配置已更新`)
        } else {
          // 添加新配置
          savedConfigs.push(config)
          ElMessage.success(`${config.userType}的异常操作监测配置已保存`)
        }

        localStorage.setItem('reportIntegrationAbnormalMonitorConfigs', JSON.stringify(savedConfigs))
        showAbnormalMonitorDialog.value = false
        abnormalMonitorLoading.value = false
      }, 1000)
    }
  })
}

// 任务依赖关系设置相关方法
// 打开任务依赖关系设置弹窗
const openTaskDependencyDialog = () => {
  // 获取当前表格中的所有任务数据
  initAvailableTasks()

  // 从localStorage读取已保存的关系配置
  const savedRelationships = localStorage.getItem('reportIntegrationTaskRelationships')
  if (savedRelationships) {
    taskRelationships.value = JSON.parse(savedRelationships)
  } else {
    taskRelationships.value = []
  }

  // 根据关系配置构建树形结构
  buildTaskDependencyTree()

  showTaskDependencyDialog.value = true
}

// 初始化可用任务列表
const initAvailableTasks = () => {
  // 从当前表格数据中获取所有任务
  availableTasks.value = [...tableData.value]
}

// 根据关系配置构建树形结构
const buildTaskDependencyTree = () => {
  // 创建任务映射
  const taskMap = new Map<string, TaskDependencyNode>()

  // 将所有任务转换为树节点
  availableTasks.value.forEach(task => {
    const node: TaskDependencyNode = {
      id: `node-${task.taskId}`,
      taskId: task.taskId,
      taskName: task.taskName,
      label: task.taskName,
      category: task.category,
      status: task.status,
      children: []
    }
    taskMap.set(task.taskId, node)
  })

  // 根据关系配置构建树形结构
  const rootNodes: TaskDependencyNode[] = []
  const childNodes = new Set<string>()

  // 标记所有有父节点的任务
  taskRelationships.value.forEach(relation => {
    if (relation.parentTaskId) {
      childNodes.add(relation.taskId)
    }
  })

  // 构建父子关系
  taskRelationships.value.forEach(relation => {
    const childNode = taskMap.get(relation.taskId)
    if (childNode && relation.parentTaskId) {
      const parentNode = taskMap.get(relation.parentTaskId)
      if (parentNode) {
        if (!parentNode.children) {
          parentNode.children = []
        }
        parentNode.children.push(childNode)
      }
    }
  })

  // 收集根节点（没有父节点的任务）
  taskMap.forEach((node, taskId) => {
    if (!childNodes.has(taskId)) {
      rootNodes.push(node)
    }
  })

  taskDependencyData.value = rootNodes
}

// 树节点点击事件
const onTaskNodeClick = (data: TaskDependencyNode) => {
  currentSelectedNode.value = data
}

// 添加任务关系（设置为子任务）
const addTaskRelation = () => {
  if (!newTaskRelationForm.value.selectedTaskId) {
    ElMessage.warning('请选择要添加的任务')
    return
  }

  const selectedTaskId = newTaskRelationForm.value.selectedTaskId
  const parentTaskId = currentSelectedNode.value?.taskId || null

  // 检查是否已存在关系
  const existingRelation = taskRelationships.value.find(r => r.taskId === selectedTaskId)
  if (existingRelation) {
    ElMessage.warning('该任务已设置了依赖关系')
    return
  }

  // 检查是否会形成循环依赖
  if (parentTaskId && wouldCreateCircularDependency(selectedTaskId, parentTaskId)) {
    ElMessage.warning('不能设置循环依赖关系')
    return
  }

  // 添加关系
  taskRelationships.value.push({
    taskId: selectedTaskId,
    parentTaskId: parentTaskId
  })

  // 重新构建树形结构
  buildTaskDependencyTree()

  // 清空表单
  newTaskRelationForm.value.selectedTaskId = ''

  // 保存到localStorage
  saveTaskRelationships()
  ElMessage.success('任务关系设置成功')
}

// 检查是否会形成循环依赖
const wouldCreateCircularDependency = (childTaskId: string, parentTaskId: string): boolean => {
  const visited = new Set<string>()

  const checkCircular = (currentTaskId: string): boolean => {
    if (visited.has(currentTaskId)) {
      return true
    }

    if (currentTaskId === childTaskId) {
      return true
    }

    visited.add(currentTaskId)

    // 查找当前任务的父任务
    const parentRelation = taskRelationships.value.find(r => r.taskId === currentTaskId)
    if (parentRelation && parentRelation.parentTaskId) {
      return checkCircular(parentRelation.parentTaskId)
    }

    return false
  }

  return checkCircular(parentTaskId)
}

// 删除任务关系
const removeTaskRelation = (data: TaskDependencyNode) => {
  ElMessageBox.confirm('确认移除该任务的依赖关系吗？', '移除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 移除该任务及其所有子任务的关系
    const removeTaskAndChildren = (taskId: string) => {
      // 移除当前任务的关系
      const index = taskRelationships.value.findIndex(r => r.taskId === taskId)
      if (index >= 0) {
        taskRelationships.value.splice(index, 1)
      }

      // 递归移除子任务的关系
      const childRelations = taskRelationships.value.filter(r => r.parentTaskId === taskId)
      childRelations.forEach(childRelation => {
        removeTaskAndChildren(childRelation.taskId)
      })
    }

    removeTaskAndChildren(data.taskId)

    // 重新构建树形结构
    buildTaskDependencyTree()

    // 保存到localStorage
    saveTaskRelationships()
    ElMessage.success('任务关系移除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 保存任务关系配置
const saveTaskRelationships = () => {
  localStorage.setItem('reportIntegrationTaskRelationships', JSON.stringify(taskRelationships.value))
}

// 重置任务依赖关系配置
const resetTaskDependencyConfig = () => {
  ElMessageBox.confirm('确认清除所有任务依赖关系吗？', '重置确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    taskRelationships.value = []
    buildTaskDependencyTree()
    saveTaskRelationships()
    ElMessage.success('任务依赖关系已清除')
  }).catch(() => {
    // 用户取消重置
  })
}

// 确认保存任务依赖关系配置
const onTaskDependencyConfirm = () => {
  taskDependencyLoading.value = true

  // 模拟保存延迟
  setTimeout(() => {
    saveTaskRelationships()
    ElMessage.success('任务依赖关系配置保存成功')
    showTaskDependencyDialog.value = false
    taskDependencyLoading.value = false
  }, 1000)
}

// 拖拽相关方法
// 判断是否允许拖拽放置
const allowDrop = (draggingNode: any, dropNode: any, type: string) => {
  // 不能拖拽到自己
  if (draggingNode.data.taskId === dropNode.data.taskId) {
    return false
  }

  // 不能拖拽到自己的子节点（避免循环依赖）
  if (isDescendant(draggingNode.data.taskId, dropNode.data.taskId)) {
    return false
  }

  // 允许拖拽到其他节点内部作为子节点，或者拖拽到节点前后作为兄弟节点
  return type === 'inner' || type === 'prev' || type === 'next'
}

// 检查是否为子孙节点
const isDescendant = (ancestorTaskId: string, descendantTaskId: string): boolean => {
  const findChildren = (taskId: string): string[] => {
    return taskRelationships.value
      .filter(r => r.parentTaskId === taskId)
      .map(r => r.taskId)
  }

  const checkDescendant = (currentTaskId: string): boolean => {
    const children = findChildren(currentTaskId)
    if (children.includes(ancestorTaskId)) {
      return true
    }
    return children.some(childId => checkDescendant(childId))
  }

  return checkDescendant(descendantTaskId)
}

// 处理拖拽放置
const onNodeDrop = (draggingNode: any, dropNode: any, dropType: string) => {
  const draggingTaskId = draggingNode.data.taskId
  const dropTaskId = dropNode.data.taskId

  // 移除原有的关系
  const existingIndex = taskRelationships.value.findIndex(r => r.taskId === draggingTaskId)
  if (existingIndex >= 0) {
    taskRelationships.value.splice(existingIndex, 1)
  }

  // 根据拖拽类型设置新的关系
  if (dropType === 'inner') {
    // 拖拽到节点内部，设置为子节点
    taskRelationships.value.push({
      taskId: draggingTaskId,
      parentTaskId: dropTaskId
    })
  } else if (dropType === 'prev' || dropType === 'next') {
    // 拖拽到节点前后，设置为兄弟节点
    const dropNodeRelation = taskRelationships.value.find(r => r.taskId === dropTaskId)
    if (dropNodeRelation && dropNodeRelation.parentTaskId) {
      // 如果目标节点有父节点，设置相同的父节点
      taskRelationships.value.push({
        taskId: draggingTaskId,
        parentTaskId: dropNodeRelation.parentTaskId
      })
    }
    // 如果目标节点是根节点，则拖拽的节点也成为根节点（不添加关系）
  }

  // 重新构建树形结构
  buildTaskDependencyTree()

  // 保存到localStorage
  saveTaskRelationships()

  ElMessage.success('任务依赖关系已更新')
}

// 导航到数据加密页面
const navigateToDataEncryption = () => {
  router.push('/reportIntegrationSpotCheck/dataEncryption')
}

// 导航到数据抽取页面
const navigateToDataExtraction = () => {
  router.push('/reportIntegrationSpotCheck/dataExtraction')
}

// 导航到结果展示页面
const navigateToResultDisplay = () => {
  router.push('/reportIntegrationSpotCheck/resultDisplay')
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="report-integration-spot-check">
    <Block
      title="报表整合度抽查"
      :enable-fixed-height="true"
      :enable-expand-content="true"
      @height-changed="onBlockHeightChanged"
      @content-expand="() => {}"
    >
      <template #topRight>
        <el-button size="small" type="primary" @click="onClickAdd">创建任务</el-button>
        <el-button size="small" type="success" @click="onExport" :disabled="selectedRows.length === 0">导出</el-button>
        <el-button size="small" type="danger" @click="onBatchDelete" :disabled="selectedRows.length === 0">批量删除</el-button>

        <el-dropdown trigger="click" style="margin-left: 8px">
          <el-button size="small" type="default">
            更多
            <el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="openDataBackupDialog">数据备份</el-dropdown-item>
              <el-dropdown-item @click="openDataMaskingDialog">数据脱敏设置</el-dropdown-item>
              <el-dropdown-item @click="openOperationLogDialog">操作监控日志</el-dropdown-item>
              <el-dropdown-item @click="openAbnormalMonitorDialog">异常操作监测内容设置</el-dropdown-item>
              <el-dropdown-item @click="openTaskDependencyDialog">任务依赖关系设置</el-dropdown-item>
              <el-dropdown-item @click="navigateToDataEncryption">数据加密</el-dropdown-item>
              <el-dropdown-item @click="navigateToDataExtraction">数据抽取</el-dropdown-item>
              <el-dropdown-item @click="navigateToResultDisplay">结果展示</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="4"
            :label-width="74"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          />
        </div>
      </template>

      <!-- 表格列表 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :defaultTableData="getCurrentPageData()"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :enable-index="false"
        :height="tableHeight"
        :loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ (pagination.page - 1) * pagination.size + row.index }}
        </template>

        <!-- 任务名称列 -->
        <template #taskName="{ row }">
          <div class="task-name-cell">
            <span>{{ row.taskName }}</span>
            <el-icon v-if="row.hasException" class="exception-icon" color="#F56C6C" title="存在异常数据">
              <Warning />
            </el-icon>
          </div>
        </template>

        <!-- 优先级列 -->
        <template #priority="{ row }">
          <el-tag
            :type="row.priority === '高' ? 'danger' : row.priority === '中' ? 'warning' : 'info'"
            size="small"
          >
            {{ row.priority }}
          </el-tag>
        </template>

        <!-- 进程列 -->
        <template #progress="{ row }">
          <div class="progress-cell">
            <el-progress
              :percentage="row.progress"
              :stroke-width="6"
              :color="row.hasException ? '#F56C6C' : '#409EFF'"
              :show-text="false"
            />
            <span class="progress-text">{{ row.progress }}%</span>
          </div>
        </template>

        <!-- 预计剩余时间列 -->
        <template #estimatedRemainingTime="{ row }">
          <div class="remaining-time-cell">
            <span
              :class="{
                'time-normal': row.status === '进行中',
                'time-completed': row.status === '已完成',
                'time-paused': row.status === '已暂停',
                'time-timeout': row.status === '已超时'
              }"
            >
              {{ row.estimatedRemainingTime }}
            </span>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <div class="status-cell">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
              :class="{ 'status-exception': row.hasException }"
            >
              {{ row.status }}
            </el-tag>
            <el-icon v-if="row.hasException" class="exception-icon" color="#F56C6C" title="存在异常数据">
              <Warning />
            </el-icon>
          </div>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <div class="action-buttons">
            <el-button
              v-for="btn in getButtons(row)"
              :key="btn.code"
              :type="btn.type"
              size="small"
              @click="onTableClickButton({ row, btn })"
              :loading="btn.code === 'delete' && loading"
            >
              {{ btn.label }}
            </el-button>
          </div>
        </template>
      </TableV2>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 创建任务对话框 -->
    <Dialog
      v-model="showDialogForm"
      title="创建报表整合度抽查任务"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="创建中"
      width="700px"
      @closed="currentRow = null; dialogForm = {}; currentDataSources = []"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
        :column-count="1"
        :label-width="100"
      />

      <!-- 数据源配置区域 -->
      <div class="data-source-config-section">
        <div class="section-header" style="display: flex; justify-content: space-between; align-items: center;">
          <h4 style="margin: 0; padding: 0; line-height: 1;">数据源配置</h4>
          <el-button type="primary" size="small" @click="onAddDataSource">
            <el-icon style="margin-right: 4px;"><Plus /></el-icon>
            添加数据源
          </el-button>
        </div>

        <div v-if="currentDataSources.length === 0" class="empty-data-source">
          <el-empty description="暂无数据源配置" :image-size="80" />
        </div>

        <div v-else class="data-source-list">
          <div
            v-for="(dataSource, index) in currentDataSources"
            :key="dataSource.id"
            class="data-source-item"
          >
            <div class="data-source-info">
              <div class="info-row">
                <span class="label">算法:</span>
                <span class="value">{{ dataSource.algorithm }}</span>
              </div>
              <div class="info-row">
                <span class="label">数据源:</span>
                <span class="value">{{ dataSource.dataSource }}</span>
              </div>
              <div class="info-row">
                <span class="label">服务器:</span>
                <span class="value">{{ dataSource.serverAddress }}:{{ dataSource.port }}</span>
              </div>
              <div class="info-row">
                <span class="label">时间范围:</span>
                <span class="value">{{ dataSource.startTime }} ~ {{ dataSource.endTime }}</span>
              </div>
              <div class="info-row">
                <span class="label">数据类型:</span>
                <span class="value">{{ dataSource.dataTypes.join(', ') }}</span>
              </div>
              <div class="info-row">
                <span class="label">数据量:</span>
                <span class="value">{{ dataSource.dataVolume }}</span>
              </div>
            </div>
            <div class="data-source-actions">
              <el-button type="primary" size="small" @click="onEditDataSource(index)">编辑</el-button>
              <el-button type="danger" size="small" @click="onDeleteDataSource(index)">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 数据源配置弹窗 -->
    <Dialog
      v-model="showDataSourceDialog"
      :title="editingDataSourceIndex >= 0 ? '编辑数据源配置' : '添加数据源配置'"
      :destroy-on-close="true"
      width="600px"
      @closed="dataSourceForm = {}; editingDataSourceIndex = -1"
      @click-confirm="onDataSourceConfirm"
    >
      <Form
        ref="dataSourceFormRef"
        v-model="dataSourceForm"
        :props="dataSourceFormProps"
        :rules="dataSourceFormRules"
        :enable-button="false"
        :column-count="1"
        :label-width="120"
      />
    </Dialog>

    <!-- 查看任务配置弹窗 -->
    <el-dialog
      v-model="showViewTaskDialog"
      title="查看任务配置"
      :destroy-on-close="true"
      width="700px"
      :visible-confirm-button="false"
      :visible-cancel-button="false"
      @closed="viewTaskForm = {}; viewTaskDataSources = []"
    >
      <Form
        v-model="viewTaskForm"
        :props="dialogFormProps.map(prop => ({ ...prop, disabled: true }))"
        :enable-button="false"
        :column-count="1"
        :label-width="100"
      />

      <!-- 数据源配置区域 -->
      <div class="data-source-config-section">
        <div class="section-header">
          <h4>数据源配置</h4>
        </div>

        <div v-if="viewTaskDataSources.length === 0" class="empty-data-source">
          <el-empty description="暂无数据源配置" :image-size="80" />
        </div>

        <div v-else class="data-source-list">
          <div
            v-for="(dataSource, index) in viewTaskDataSources"
            :key="dataSource.id"
            class="data-source-item view-mode"
          >
            <div class="data-source-info">
              <div class="info-row">
                <span class="label">算法:</span>
                <span class="value">{{ dataSource.algorithm }}</span>
              </div>
              <div class="info-row">
                <span class="label">数据源:</span>
                <span class="value">{{ dataSource.dataSource }}</span>
              </div>
              <div class="info-row">
                <span class="label">服务器:</span>
                <span class="value">{{ dataSource.serverAddress }}:{{ dataSource.port }}</span>
              </div>
              <div class="info-row">
                <span class="label">时间范围:</span>
                <span class="value">{{ dataSource.startTime }} ~ {{ dataSource.endTime }}</span>
              </div>
              <div class="info-row">
                <span class="label">数据类型:</span>
                <span class="value">{{ dataSource.dataTypes.join(', ') }}</span>
              </div>
              <div class="info-row">
                <span class="label">数据量:</span>
                <span class="value">{{ dataSource.dataVolume }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 数据备份主弹窗 -->
    <el-dialog
      v-model="showDataBackupDialog"
      title="数据备份"
      :destroy-on-close="true"
      width="1200px"
      @closed="dataBackupTableData = []; dataBackupSearchForm = { backupName: '', startTime: '', endTime: '' }; dataBackupSelectedRows = []"
    >
      <div class="data-backup-dialog-content">
        <!-- 搜索区域 -->
        <div class="data-backup-search-area">
          <el-form :model="dataBackupSearchForm" inline>
            <el-form-item label="备份名称">
              <el-input
                v-model="dataBackupSearchForm.backupName"
                placeholder="请输入备份名称"
                clearable
                style="width: 200px;"
                @input="onDataBackupSearch"
                @clear="onDataBackupSearch"
              />
            </el-form-item>
            <el-form-item label="备份时间">
              <el-date-picker
                v-model="dataBackupSearchForm.startTime"
                type="datetime"
                placeholder="开始时间"
                style="width: 180px;"
                @change="onDataBackupSearch"
              />
            </el-form-item>
            <el-form-item label="至">
              <el-date-picker
                v-model="dataBackupSearchForm.endTime"
                type="datetime"
                placeholder="结束时间"
                style="width: 180px;"
                @change="onDataBackupSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onDataBackupSearch">查询</el-button>
              <el-button @click="dataBackupSearchForm = { backupName: '', startTime: '', endTime: '' }; onDataBackupSearch()">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="data-backup-toolbar">
          <el-button type="primary" @click="onAddDataBackup">新增数据备份任务</el-button>
          <el-button type="success" @click="onDataBackupExport" :disabled="dataBackupSelectedRows.length === 0">导出</el-button>
        </div>

        <!-- 数据备份表格 -->
        <TableV2
          :columns="dataBackupColumns"
          :defaultTableData="getCurrentPageDataBackupData()"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="true"
          :enable-index="false"
          :height="400"
          :loading="dataBackupLoading"
          @selection-change="handleDataBackupSelectionChange"
        >
          <!-- 序号列 -->
          <template #index="{ row }">
            {{ (dataBackupPagination.page - 1) * dataBackupPagination.size + row.index }}
          </template>

          <!-- 状态列 -->
          <template #status="{ row }">
            <el-tag
              :type="getDataBackupStatusType(row.status)"
              size="small"
            >
              {{ row.status }}
            </el-tag>
          </template>

          <!-- 操作列 -->
          <template #action="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="onEditDataBackup(row)">编辑</el-button>
              <el-button type="danger" size="small" @click="onDeleteDataBackup(row)">删除</el-button>
            </div>
          </template>
        </TableV2>

        <!-- 分页 -->
        <div class="data-backup-pagination" style="margin-top: 16px; text-align: right;">
          <Pagination
            :total="dataBackupPagination.total"
            :current-page="dataBackupPagination.page"
            :page-size="dataBackupPagination.size"
            :page-sizes="[5, 10, 20, 50]"
            @current-change="onDataBackupPaginationChange($event, 'page')"
            @size-change="onDataBackupPaginationChange($event, 'size')"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 新增/修改数据备份弹窗 -->
    <Dialog
      v-model="showDataBackupFormDialog"
      :title="editingDataBackupIndex >= 0 ? '修改数据备份任务' : '新增数据备份任务'"
      :destroy-on-close="true"
      :loading="dataBackupLoading"
      loading-text="保存中"
      width="600px"
      @closed="dataBackupForm = {}; editingDataBackupIndex = -1"
      @click-confirm="onDataBackupFormConfirm"
    >
      <Form
        ref="dataBackupFormRef"
        v-model="dataBackupForm"
        :props="dataBackupFormProps"
        :rules="dataBackupFormRules"
        :enable-button="false"
        :column-count="1"
        :label-width="100"
      />
    </Dialog>

    <!-- 操作监控日志弹窗 -->
    <el-dialog
      v-model="showOperationLogDialog"
      title="操作监控日志"
      :destroy-on-close="true"
      width="1400px"
      @closed="operationLogTableData = []; operationLogSearchForm = { startTime: '', endTime: '', operationType: '', operator: '' }; operationLogSelectedRows = []"
    >
      <div class="operation-log-dialog-content">
        <!-- 搜索区域 -->
        <div class="operation-log-search-area">
          <el-form :model="operationLogSearchForm" inline>
            <el-form-item label="操作时间">
              <el-date-picker
                v-model="operationLogSearchForm.startTime"
                type="datetime"
                placeholder="开始时间"
                style="width: 180px;"
                @change="onOperationLogSearch"
              />
            </el-form-item>
            <el-form-item label="至">
              <el-date-picker
                v-model="operationLogSearchForm.endTime"
                type="datetime"
                placeholder="结束时间"
                style="width: 180px;"
                @change="onOperationLogSearch"
              />
            </el-form-item>
            <el-form-item label="操作类型">
              <el-input
                v-model="operationLogSearchForm.operationType"
                placeholder="请输入操作类型"
                clearable
                style="width: 150px;"
                @input="onOperationLogSearch"
                @clear="onOperationLogSearch"
              />
            </el-form-item>
            <el-form-item label="操作人员">
              <el-input
                v-model="operationLogSearchForm.operator"
                placeholder="请输入操作人员"
                clearable
                style="width: 150px;"
                @input="onOperationLogSearch"
                @clear="onOperationLogSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onOperationLogSearch">查询</el-button>
              <el-button @click="operationLogSearchForm = { startTime: '', endTime: '', operationType: '', operator: '' }; onOperationLogSearch()">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="operation-log-toolbar">
          <el-button type="success" @click="onExportAllOperationLogs">导出所有</el-button>
          <el-button type="success" @click="onExportSelectedOperationLogs" :disabled="operationLogSelectedRows.length === 0">导出选中</el-button>
        </div>

        <!-- 操作日志表格 -->
        <TableV2
          :columns="operationLogColumns"
          :defaultTableData="getCurrentPageOperationLogData()"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="true"
          :enable-index="false"
          :height="450"
          :loading="operationLogLoading"
          @selection-change="handleOperationLogSelectionChange"
        >
          <!-- 序号列 -->
          <template #index="{ row }">
            {{ (operationLogPagination.page - 1) * operationLogPagination.size + row.index }}
          </template>

          <!-- 操作类型列 -->
          <template #operationType="{ row }">
            <el-tag
              :type="getOperationTypeColor(row.operationType)"
              size="small"
            >
              {{ row.operationType }}
            </el-tag>
          </template>
        </TableV2>

        <!-- 分页 -->
        <div class="operation-log-pagination" style="margin-top: 16px; text-align: right;">
          <Pagination
            :total="operationLogPagination.total"
            :current-page="operationLogPagination.page"
            :page-size="operationLogPagination.size"
            :page-sizes="[10, 20, 50, 100]"
            @current-change="onOperationLogPaginationChange($event, 'page')"
            @size-change="onOperationLogPaginationChange($event, 'size')"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 异常操作监测内容设置弹窗 -->
    <Dialog
      v-model="showAbnormalMonitorDialog"
      title="异常操作监测内容设置"
      :destroy-on-close="true"
      :loading="abnormalMonitorLoading"
      loading-text="保存中"
      width="600px"
      @closed="abnormalMonitorForm = {}"
      @click-confirm="onAbnormalMonitorFormConfirm"
    >
      <el-form
        ref="abnormalMonitorFormRef"
        :model="abnormalMonitorForm"
        :rules="abnormalMonitorFormRules"
        label-width="120px"
        class="abnormal-monitor-form"
      >
        <el-form-item label="用户类型" prop="userType">
          <el-select
            v-model="abnormalMonitorForm.userType"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option label="管理员" value="管理员" />
            <el-option label="普通用户" value="普通用户" />
            <el-option label="审核员" value="审核员" />
          </el-select>
        </el-form-item>

        <el-form-item label="操作类型" prop="operationTypes">
          <el-checkbox-group v-model="abnormalMonitorForm.operationTypes">
            <el-checkbox value="登录">登录</el-checkbox>
            <el-checkbox value="删除">删除</el-checkbox>
            <el-checkbox value="修改">修改</el-checkbox>
            <el-checkbox value="添加">添加</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="时间段" prop="startTime">
          <div class="time-range-container">
            <el-form-item prop="startTime" style="display: inline-block; margin: 0; width: 45%;">
              <el-date-picker
                v-model="abnormalMonitorForm.startTime"
                type="datetime"
                placeholder="开始时间"
                style="width: 100%"
              />
            </el-form-item>
            <span style="display: inline-block; width: 10%; text-align: center; margin: 0 2.5%;">至</span>
            <el-form-item prop="endTime" style="display: inline-block; margin: 0; width: 45%;">
              <el-date-picker
                v-model="abnormalMonitorForm.endTime"
                type="datetime"
                placeholder="结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </div>
        </el-form-item>

        <el-form-item label="阈值设定">
          <div class="threshold-container">
            <div class="threshold-item">
              <span>连续登录次数超过</span>
              <el-form-item prop="loginThreshold" style="display: inline-block; margin: 0 8px; width: 100px;">
                <el-input-number
                  v-model="abnormalMonitorForm.loginThreshold"
                  :min="1"
                  :max="1000"
                  controls-position="right"
                  style="width: 100px"
                />
              </el-form-item>
              <span>次</span>
            </div>
            <div class="threshold-item" style="margin-top: 12px;">
              <span>添加文件超过</span>
              <el-form-item prop="fileSizeLimit" style="display: inline-block; margin: 0 8px; width: 120px;">
                <el-input-number
                  v-model="abnormalMonitorForm.fileSizeLimit"
                  :min="0.1"
                  :max="10240"
                  :precision="1"
                  :step="0.1"
                  controls-position="right"
                  style="width: 120px"
                />
              </el-form-item>
              <span>MB</span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 数据脱敏设置弹窗 -->
    <Dialog
      v-model="showDataMaskingDialog"
      title="报表整合度抽查数据脱敏配置"
      :destroy-on-close="true"
      :loading="dataMaskingLoading"
      loading-text="保存中"
      width="600px"
      @closed="dataMaskingForm = {}; testResult = ''"
      @click-confirm="onDataMaskingFormConfirm"
    >
      <el-form
        ref="dataMaskingFormRef"
        :model="dataMaskingForm"
        :rules="dataMaskingFormRules"
        label-width="120px"
        class="data-masking-form"
      >
        <el-form-item label="脱敏规则名称" prop="ruleName">
          <el-input
            v-model="dataMaskingForm.ruleName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>

        <el-form-item label="字段类型" prop="fieldType">
          <el-radio-group v-model="dataMaskingForm.fieldType">
            <el-radio value="字符串">字符串</el-radio>
            <el-radio value="小数">小数</el-radio>
            <el-radio value="日期时间">日期时间</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="脱敏算法" prop="algorithm">
          <el-select
            v-model="dataMaskingForm.algorithm"
            placeholder="保留前后位数算法"
            style="width: 100%"
          >
            <el-option label="保留前后位数算法" value="保留前后位数算法" />
            <el-option label="完全替换算法" value="完全替换算法" />
            <el-option label="部分显示算法" value="部分显示算法" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="dataMaskingForm.algorithm === '保留前后位数算法'">
          <template #label>
            <span></span>
          </template>
          <div class="masking-config-row">
            <span>保留字段的前</span>
            <el-form-item prop="frontKeep" style="display: inline-block; margin: 0 8px; width: 80px;">
              <el-input-number
                v-model="dataMaskingForm.frontKeep"
                :min="0"
                :max="50"
                controls-position="right"
                style="width: 80px"
              />
            </el-form-item>
            <span>位和后</span>
            <el-form-item prop="backKeep" style="display: inline-block; margin: 0 8px; width: 80px;">
              <el-input-number
                v-model="dataMaskingForm.backKeep"
                :min="0"
                :max="50"
                controls-position="right"
                style="width: 80px"
              />
            </el-form-item>
            <span>位，其余字符替换成*</span>
          </div>
        </el-form-item>

        <el-form-item>
          <template #label>
            <span></span>
          </template>
          <div class="test-section">
            <el-input
              v-model="dataMaskingForm.testInput"
              placeholder="请输入内容"
              style="width: 300px; margin-right: 8px;"
            />
            <el-button type="primary" @click="testDataMasking">测试</el-button>
          </div>
        </el-form-item>

        <el-form-item v-if="testResult">
          <template #label>
            <span>测试结果：</span>
          </template>
          <div class="test-result">
            <el-input
              :value="testResult"
              readonly
              style="width: 300px;"
            />
          </div>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 任务日志弹窗 -->
    <el-dialog
      v-model="showLogDialog"
      :title="`任务ID：${currentTaskId} - 操作日志`"
      :destroy-on-close="true"
      width="900px"
      @closed="currentTaskLog = []; currentTaskName = ''; currentTaskId = ''; logSearchKeyword = ''; allTaskLogs = []"
    >
      <div class="log-dialog-content">
        <!-- 搜索区域 -->
        <div class="log-search-area">
          <el-input
            v-model="logSearchKeyword"
            placeholder="请输入用户名称搜索"
            clearable
            style="width: 300px; margin-bottom: 16px;"
            @input="onLogSearch"
            @clear="onLogSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 日志表格 -->
        <TableV2
          :columns="logColumns"
          :defaultTableData="currentTaskLog"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="false"
          :enable-index="false"
          :height="350"
          :loading="false"
        >
          <!-- 序号列 -->
          <template #index="{ row }">
            {{ (logPagination.page - 1) * logPagination.size + row.index }}
          </template>

          <!-- 操作类型列 -->
          <template #operationType="{ row }">
            <el-tag
              :type="getOperationTypeColor(row.operationType)"
              size="small"
            >
              {{ row.operationType }}
            </el-tag>
          </template>
        </TableV2>

        <!-- 分页 -->
        <div class="log-pagination" style="margin-top: 16px; text-align: right;">
          <Pagination
            :total="logPagination.total"
            :current-page="logPagination.page"
            :page-size="logPagination.size"
            :page-sizes="[5, 10, 20, 50]"
            @current-change="onLogPaginationChange($event, 'page')"
            @size-change="onLogPaginationChange($event, 'size')"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 任务依赖关系设置弹窗 -->
    <Dialog
      v-model="showTaskDependencyDialog"
      title="任务依赖关系设置"
      :destroy-on-close="true"
      :loading="taskDependencyLoading"
      loading-text="保存中"
      width="800px"
      @closed="currentSelectedNode = null; newTaskRelationForm = { selectedTaskId: '', parentTaskId: '' }"
      @click-confirm="onTaskDependencyConfirm"
    >
      <div class="task-dependency-container">
        <!-- 操作区域 -->
        <div class="task-dependency-toolbar">
          <div class="toolbar-info">
            <el-alert
              title="拖拽操作说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <span>拖拽任务节点可快速设置依赖关系：拖拽到其他节点内部设为子任务，拖拽到节点前后设为兄弟任务</span>
              </template>
            </el-alert>
          </div>

          <div class="add-task-form">
            <el-button
              type="warning"
              size="small"
              @click="resetTaskDependencyConfig"
            >
              清除所有关系
            </el-button>
          </div>

          <div class="selected-info" v-if="currentSelectedNode">
            <span class="selected-label">当前选中：</span>
            <el-tag type="primary">{{ currentSelectedNode.taskName }}</el-tag>
            <el-tag type="info" size="small">{{ currentSelectedNode.category }}</el-tag>
          </div>
        </div>

        <!-- 树形结构展示区域 -->
        <div class="task-dependency-tree">
          <el-tree
            ref="taskDependencyTreeRef"
            :data="taskDependencyData"
            node-key="id"
            :default-expand-all="true"
            :highlight-current="true"
            draggable
            :allow-drop="allowDrop"
            @node-click="onTaskNodeClick"
            @node-drop="onNodeDrop"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <div class="node-info">
                  <span class="node-label">{{ data.taskName }}</span>
                  <el-tag size="small" :type="data.status === '进行中' ? 'success' : data.status === '已暂停' ? 'warning' : 'info'">
                    {{ data.status }}
                  </el-tag>
                  <el-tag size="small" type="info">{{ data.category }}</el-tag>
                </div>
                <div class="node-actions">
                  <el-button
                    type="danger"
                    size="small"
                    @click.stop="removeTaskRelation(data)"
                  >
                    移除关系
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>

        <!-- 说明信息 -->
        <div class="task-dependency-tips">
          <el-alert
            title="详细使用说明"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul>
                <li><strong>拖拽操作（推荐）：</strong>直接拖拽任务节点到目标位置设置依赖关系</li>
                <li><strong>拖拽到节点内部：</strong>设置为该节点的子任务（依赖关系）</li>
                <li><strong>拖拽到节点前后：</strong>设置为兄弟任务（相同层级）</li>
                <li><strong>手动添加：</strong>从下拉列表选择未分配的任务添加到树中</li>
                <li><strong>移除关系：</strong>点击"移除关系"按钮或拖拽到根级别</li>
                <li><strong>智能保护：</strong>系统自动阻止循环依赖和无效操作</li>
                <li><strong>自动保存：</strong>所有操作立即保存，页面刷新后保持</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '报表整合度抽查'
  }
}
</route>

<style scoped lang="scss">
.report-integration-spot-check {
  .search {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 16px;
  }

  .task-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .exception-icon {
      font-size: 16px;
      cursor: help;
    }
  }

  .progress-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;

    .el-progress {
      flex: 1;
      min-width: 80px;
    }

    .progress-text {
      font-size: 12px;
      color: #666;
      min-width: 35px;
      white-space: nowrap;
    }
  }

  .remaining-time-cell {
    display: flex;
    align-items: center;
    justify-content: center;

    .time-normal {
      color: #409EFF;
      font-weight: 500;
    }

    .time-completed {
      color: #67C23A;
      font-weight: 500;
    }

    .time-paused {
      color: #E6A23C;
      font-weight: 500;
    }

    .time-timeout {
      color: #F56C6C;
      font-weight: 500;
    }
  }

  .status-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-exception {
      border-color: #F56C6C;
    }

    .exception-icon {
      font-size: 14px;
      cursor: help;
    }
  }

  .action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;

    .el-button {
      margin: 0;
    }
  }

  .log-dialog-content {
    .log-search-area {
      padding: 8px 0;
      border-bottom: 1px solid #ebeef5;
      margin-bottom: 16px;
    }

    .el-table {
      border: 1px solid #ebeef5;
    }

    .log-pagination {
      display: flex;
      justify-content: flex-end;
      padding-top: 8px;
      border-top: 1px solid #ebeef5;
    }
  }

  // 数据源配置区域样式
  .data-source-config-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;
    min-height: 120px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 0;

      h4 {
        margin: 0;
        padding: 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        line-height: 32px; // 与按钮高度对齐
        flex: 1;
      }

      .el-button {
        flex-shrink: 0;

        .el-icon {
          margin-right: 4px;
        }
      }
    }

    .empty-data-source {
      text-align: center;
      padding: 20px 0;
    }

    .data-source-list {
      .data-source-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 12px;
        margin-bottom: 8px;
        background-color: #ffffff;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
        }

        &:last-child {
          margin-bottom: 0;
        }

        .data-source-info {
          flex: 1;

          .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 4px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              font-weight: 500;
              color: #606266;
              min-width: 60px;
              margin-right: 8px;
            }

            .value {
              color: #303133;
              flex: 1;
            }
          }
        }

        .data-source-actions {
          display: flex;
          gap: 8px;
          margin-left: 16px;
          flex-shrink: 0;

          .el-button {
            min-width: 60px;
          }
        }

        // 查看模式样式
        &.view-mode {
          .data-source-actions {
            display: none;
          }
        }
      }
    }
  }
}

:deep(.el-progress-bar__outer) {
  background-color: #e4e7ed;
}

:deep(.el-progress-bar__inner) {
  transition: width 0.3s ease;
}

// 数据备份弹窗样式
.data-backup-dialog-content {
  .data-backup-search-area {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 16px;

    .el-form {
      margin: 0;
    }

    .el-form-item {
      margin-bottom: 0;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .data-backup-toolbar {
    margin-bottom: 16px;
    display: flex;
    gap: 8px;
  }

  .action-buttons {
    display: flex;
    gap: 4px;

    .el-button {
      margin: 0;
    }
  }

  .data-backup-pagination {
    display: flex;
    justify-content: flex-end;
    padding-top: 8px;
    border-top: 1px solid #ebeef5;
  }
}

// 数据脱敏弹窗样式
.data-masking-form {
  .masking-config-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;

    span {
      color: #606266;
      font-size: 14px;
      white-space: nowrap;
    }

    .el-form-item {
      margin-bottom: 0 !important;
    }
  }

  .test-section {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .test-result {
    .el-input {
      background-color: #f5f7fa;

      :deep(.el-input__inner) {
        background-color: #f5f7fa;
        color: #409eff;
        font-weight: 500;
      }
    }
  }

  .el-radio-group {
    .el-radio {
      margin-right: 24px;
    }
  }
}

// 操作监控日志弹窗样式
.operation-log-dialog-content {
  .operation-log-search-area {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 16px;

    .el-form {
      margin: 0;
    }

    .el-form-item {
      margin-bottom: 0;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .operation-log-toolbar {
    margin-bottom: 16px;
    display: flex;
    gap: 8px;
  }

  .operation-log-pagination {
    display: flex;
    justify-content: flex-end;
    padding-top: 8px;
    border-top: 1px solid #ebeef5;
  }
}

// 异常操作监测弹窗样式
.abnormal-monitor-form {
  .time-range-container {
    display: flex;
    align-items: center;
    width: 100%;

    .el-form-item {
      margin-bottom: 0 !important;
    }
  }

  .threshold-container {
    .threshold-item {
      display: flex;
      align-items: center;
      gap: 4px;

      span {
        color: #606266;
        font-size: 14px;
        white-space: nowrap;
      }

      .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .el-checkbox-group {
    .el-checkbox {
      margin-right: 24px;
    }
  }
}

// 任务依赖关系设置弹窗样式
.task-dependency-container {
  .task-dependency-toolbar {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;

    .toolbar-info {
      margin-bottom: 16px;

      .el-alert {
        :deep(.el-alert__content) {
          span {
            font-size: 13px;
            color: #606266;
          }
        }
      }
    }

    .add-task-form {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      gap: 8px;
    }

    .selected-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .selected-label {
        color: #606266;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .task-dependency-tree {
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 16px;
    min-height: 300px;
    max-height: 400px;
    overflow-y: auto;

    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 8px;

      .node-info {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;

        .node-label {
          color: #303133;
          font-weight: 500;
        }
      }

      .node-actions {
        opacity: 0;
        transition: opacity 0.3s;
      }

      &:hover .node-actions {
        opacity: 1;
      }
    }

    :deep(.el-tree-node__content) {
      height: 32px;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    :deep(.el-tree-node.is-current > .el-tree-node__content) {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 4px;
    }

    // 拖拽相关样式
    :deep(.el-tree-node__content.is-dragging) {
      opacity: 0.6;
      background-color: #f0f9ff;
      border: 2px dashed #409eff;
    }

    :deep(.el-tree-node.is-drop-inner > .el-tree-node__content) {
      background-color: #e6f7ff;
      border: 2px solid #409eff;
    }

    :deep(.el-tree-node__content) {
      transition: all 0.3s ease;
      cursor: move;

      &:hover {
        background-color: #f5f7fa;
        border-radius: 4px;
      }
    }
  }

  .task-dependency-tips {
    .el-alert {
      :deep(.el-alert__content) {
        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 4px;
            color: #606266;
            font-size: 13px;
            line-height: 1.5;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}
</style>
