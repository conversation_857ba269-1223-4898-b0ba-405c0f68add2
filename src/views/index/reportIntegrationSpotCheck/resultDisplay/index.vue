<script setup lang="ts" name="resultDisplay">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Star, Warning, Download, View, Printer, Share } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const currentScore = ref(75) // 当前整合度得分

// 重庆市区县数据
const chongqingDistricts = [
  '渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区', '大渡口区',
  '渝北区', '巴南区', '北碚区', '綦江区', '大足区', '长寿区',
  '江津区', '合川区', '永川区', '南川区', '璧山区', '铜梁区',
  '潼南区', '荣昌区'
]

// 整合度排名数据
const rankingData = ref<any[]>([])

// 趋势图表实例
const trendChartRef = ref()
const trendChart = ref<echarts.ECharts>()

// 对比图表实例
const comparisonChartRef = ref()
const comparisonChart = ref<echarts.ECharts>()

// 对比选择器数据
const comparisonForm = ref({
  district1: '渝中区',
  district2: '江北区'
})

// 生成整合度报告对话框
const showReportDialog = ref(false)
const reportActions = [
  { label: '查看报告', icon: View, action: 'view' },
  { label: '打印报告', icon: Printer, action: 'print' },
  { label: '分享报告', icon: Share, action: 'share' },
  { label: '导出PDF', icon: Download, action: 'export' }
]

// 查看报告预览对话框
const showReportPreviewDialog = ref(false)
const reportPreviewLoading = ref(false)

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// 获取得分颜色
const getScoreColor = (score: number) => {
  if (score >= 80) return '#67C23A' // 绿色
  if (score >= 60) return '#E6A23C' // 黄色
  return '#F56C6C' // 红色
}

// 获取得分等级
const getScoreLevel = (score: number) => {
  if (score >= 80) return '优秀'
  if (score >= 60) return '良好'
  return '待改进'
}

// 生成排名数据
const generateRankingData = () => {
  const data = chongqingDistricts.map((district, index) => {
    const score = Math.floor(Math.random() * 45) + 50 // 50-95分
    return {
      id: `district_${index}`,
      rank: index + 1,
      district,
      score,
      isFirst: false,
      isLast: false
    }
  })
  
  // 按分数降序排列
  data.sort((a, b) => b.score - a.score)
  
  // 重新设置排名
  data.forEach((item, index) => {
    item.rank = index + 1
    item.isFirst = index === 0
    item.isLast = index >= data.length - 2 // 最后两名
  })
  
  return data
}

// 生成趋势数据
const generateTrendData = () => {
  const months = []
  const scores = []
  const now = new Date()

  for (let i = 11; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    months.push(`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`)
    scores.push(Math.floor(Math.random() * 45) + 50) // 50-95分
  }

  return { months, scores }
}

// 获取分数等级文字
const getScoreLevelText = (score: number): string => {
  if (score < 40) return '严重偏低'
  if (score < 50) return '明显偏低'
  if (score < 60) return '整合度过低'
  return '正常'
}

// 生成预警标识数据
const generateWarningMarks = (scores: number[]): any[] => {
  const warningMarks: any[] = []
  scores.forEach((score, index) => {
    if (score < 60) {
      warningMarks.push({
        coord: [index, score],
        symbol: 'none',
        label: {
          show: true,
          position: 'top',
          distance: 15,
          formatter: () => {
            return `{scoreValue|${score}分}\n{levelText|${getScoreLevelText(score)}}\n{warning|⚠️}`
          },
          rich: {
            scoreValue: {
              fontSize: 12,
              color: '#F56C6C',
              fontWeight: 'bold',
              align: 'center',
              lineHeight: 16
            },
            levelText: {
              fontSize: 10,
              color: '#F56C6C',
              align: 'center',
              lineHeight: 14
            },
            warning: {
              fontSize: 14,
              color: '#F56C6C',
              align: 'center',
              lineHeight: 18
            }
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#F56C6C',
          borderWidth: 1,
          borderRadius: 4,
          padding: [4, 6],
          shadowColor: 'rgba(245, 108, 108, 0.3)',
          shadowBlur: 4,
          shadowOffsetY: 2
        }
      })
    }
  })
  return warningMarks
}

// 生成对比数据
const generateComparisonData = (_district1: string, _district2: string) => {
  const { months } = generateTrendData()
  const scores1 = months.map(() => Math.floor(Math.random() * 45) + 50)
  const scores2 = months.map(() => Math.floor(Math.random() * 45) + 50)

  return {
    months,
    district1Data: scores1,
    district2Data: scores2
  }
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart.value = echarts.init(trendChartRef.value)
  const { months, scores } = generateTrendData()
  
  const option = {
    title: {
      text: '整合度趋势曲线图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        const score = data.value
        const warning = score < 60 ? '<br/><span style="color: #F56C6C; font-weight: bold;">⚠️ 低分预警</span>' : ''
        return `${data.axisValue}<br/>整合度得分: ${score}分${warning}`
      }
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}分'
      }
    },
    series: [{
      name: '整合度得分',
      type: 'line',
      data: scores,
      smooth: true,
      lineStyle: {
        width: 3,
        color: '#409EFF'
      },
      itemStyle: {
        color: '#409EFF'
      },
      markPoint: {
        symbol: 'none',
        symbolSize: 0,
        data: generateWarningMarks(scores)
      }
    }]
  }
  
  trendChart.value.setOption(option)
}

// 初始化对比图表
const initComparisonChart = () => {
  if (!comparisonChartRef.value) return
  
  comparisonChart.value = echarts.init(comparisonChartRef.value)
  updateComparisonChart()
}

// 更新对比图表
const updateComparisonChart = () => {
  if (!comparisonChart.value) return
  
  const { months, district1Data, district2Data } = generateComparisonData(
    comparisonForm.value.district1,
    comparisonForm.value.district2
  )
  
  const option = {
    title: {
      text: '整合度对比折线图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: [comparisonForm.value.district1, comparisonForm.value.district2],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}分'
      }
    },
    series: [
      {
        name: comparisonForm.value.district1,
        type: 'line',
        data: district1Data,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409EFF'
        }
      },
      {
        name: comparisonForm.value.district2,
        type: 'line',
        data: district2Data,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#67C23A'
        }
      }
    ]
  }
  
  comparisonChart.value.setOption(option)
}

// 对比地区变化处理
const handleComparisonChange = () => {
  updateComparisonChart()
}

// 生成整合度报告
const generateReport = () => {
  showReportDialog.value = true
}

// 处理报告操作
const handleReportAction = async (action: string) => {
  switch (action) {
    case 'view':
      showReportDialog.value = false
      await openReportPreview()
      break
    case 'print':
      showReportDialog.value = false
      await printReport()
      break
    case 'share':
      showReportDialog.value = false
      await shareReport()
      break
    case 'export':
      showReportDialog.value = false
      await exportToPDF()
      break
  }
}

// 打开报告预览
const openReportPreview = async () => {
  try {
    reportPreviewLoading.value = true
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    showReportPreviewDialog.value = true
  } catch (error) {
    console.error('打开报告预览失败:', error)
    ElMessage.error('打开报告预览失败，请重试')
  } finally {
    reportPreviewLoading.value = false
  }
}

// 导出PDF功能
const exportToPDF = async () => {
  try {
    loading.value = true

    // 显示提示信息
    ElMessageBox.alert(
      '当前系统使用浏览器打印功能来生成PDF报告。如需完整的PDF导出功能，请联系系统管理员安装相关组件。',
      'PDF导出说明',
      {
        confirmButtonText: '继续打印',
        type: 'info'
      }
    ).then(async () => {
      await printReport()
    }).catch(() => {
      // 用户取消
    })

  } catch (error) {
    console.error('导出PDF失败:', error)
    ElMessage.error('导出PDF失败，请重试')
  } finally {
    loading.value = false
  }
}

// 浏览器打印功能（备选方案）
const printReport = async () => {
  try {
    // 确保报告预览已打开
    if (!showReportPreviewDialog.value) {
      await openReportPreview()
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    const reportElement = document.getElementById('report-content')
    if (!reportElement) {
      throw new Error('找不到报告内容元素')
    }

    // 创建新窗口用于打印
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置')
    }

    // 创建打印文档内容
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>重庆市报表整合度分析报告</title>
        <style>
          body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
          }
          .report-header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #409EFF; }
          .report-title { font-size: 24px; font-weight: bold; margin: 0 0 15px 0; }
          .report-meta { font-size: 14px; color: #666; }
          .report-meta p { margin: 5px 0; }
          .section-title { font-size: 18px; font-weight: bold; margin: 20px 0 15px 0; padding-left: 10px; border-left: 4px solid #409EFF; }
          .report-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
          .report-table th, .report-table td { padding: 8px; text-align: center; border: 1px solid #ddd; }
          .report-table th { background-color: #f5f5f5; font-weight: bold; }
          .score-value { font-size: 36px; font-weight: bold; display: block; margin-bottom: 10px; }
          .current-score-display { text-align: center; background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
          ul { padding-left: 20px; }
          li { margin-bottom: 8px; }
          .report-footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #999; }
          @media print {
            body { margin: 0; }
            .report-header { page-break-after: avoid; }
            .section-title { page-break-after: avoid; }
            .report-table { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        ${reportElement.innerHTML}
      </body>
      </html>
    `

    // 写入文档内容（虽然document.write已弃用，但这是浏览器打印功能的标准做法）
    printWindow.document.open()
    printWindow.document.write(printContent)
    printWindow.document.close()

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }

    ElMessage.success('已打开打印预览窗口')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败，请重试')
  }
}

// 分享报告功能
const shareReport = async () => {
  try {
    loading.value = true
    ElMessage.info('正在生成分享链接...')

    // 模拟生成分享链接的过程
    await new Promise(resolve => setTimeout(resolve, 1500))

    const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/resultDisplay?share=${Date.now()}`

    // 尝试使用Web Share API（如果支持）
    if (navigator.share) {
      await navigator.share({
        title: '重庆市报表整合度分析报告',
        text: `当前整合度得分：${currentScore.value}分，查看详细分析报告`,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      // 备选方案：复制到剪贴板
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareUrl)
        ElMessage.success('分享链接已复制到剪贴板')
      } else {
        // 最后的备选方案：显示链接
        ElMessageBox.alert(shareUrl, '分享链接', {
          confirmButtonText: '确定',
          type: 'info'
        })
      }
    }
  } catch (error: any) {
    console.error('分享失败:', error)
    if (error?.name === 'AbortError') {
      // 用户取消分享
      return
    }
    ElMessage.error('分享失败，请重试')
  } finally {
    loading.value = false
  }
}

// 窗口大小变化时的图表自适应
const handleChartResize = () => {
  if (trendChart.value) {
    trendChart.value.resize()
  }
  if (comparisonChart.value) {
    comparisonChart.value.resize()
  }
}

// 防抖处理的窗口大小变化
let resizeTimer: NodeJS.Timeout | null = null
const debouncedHandleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    handleChartResize()
  }, 300)
}

// 初始化数据
const initData = async () => {
  try {
    loading.value = true

    // 模拟数据加载延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 从localStorage获取或生成数据
    const savedScore = localStorage.getItem('reportIntegrationCurrentScore')
    if (savedScore) {
      currentScore.value = parseInt(savedScore)
    } else {
      currentScore.value = Math.floor(Math.random() * 45) + 50
      localStorage.setItem('reportIntegrationCurrentScore', currentScore.value.toString())
    }

    const savedRanking = localStorage.getItem('reportIntegrationRankingData')
    if (savedRanking) {
      try {
        rankingData.value = JSON.parse(savedRanking)
      } catch (error) {
        console.warn('解析排名数据失败，重新生成:', error)
        rankingData.value = generateRankingData()
        localStorage.setItem('reportIntegrationRankingData', JSON.stringify(rankingData.value))
      }
    } else {
      rankingData.value = generateRankingData()
      localStorage.setItem('reportIntegrationRankingData', JSON.stringify(rankingData.value))
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('数据加载失败，请刷新页面重试')
    // 使用默认数据
    currentScore.value = 75
    rankingData.value = generateRankingData()
  } finally {
    loading.value = false
  }
}



// 生命周期
onMounted(async () => {
  await initData()

  nextTick(() => {
    try {
      initTrendChart()
      initComparisonChart()

      // 监听窗口大小变化，使用防抖处理
      window.addEventListener('resize', debouncedHandleResize)
    } catch (error) {
      console.error('图表初始化失败:', error)
      ElMessage.error('图表加载失败，请刷新页面重试')
    }
  })
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', debouncedHandleResize)
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  if (trendChart.value) {
    trendChart.value.dispose()
  }
  if (comparisonChart.value) {
    comparisonChart.value.dispose()
  }
})
</script>

<route>
{
  meta: {
    title: '结果展示',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="result-display">
    <Block
      title="报表整合度分析结果"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
    >
      <template #topRight>
        <el-button 
          size="small" 
          type="default" 
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        
        <el-button size="small" type="primary" @click="generateReport">
          生成整合度报告
        </el-button>
      </template>

      <!-- 主要内容区域 -->
      <div class="result-content">
        <!-- 第一行：当前得分和排名表格 -->
        <div class="top-row">
          <!-- 当前整合度得分 -->
          <div class="score-card">
            <el-card class="score-display">
              <div class="score-content">
                <div class="score-number" :style="{ color: getScoreColor(currentScore) }">
                  {{ currentScore }}
                </div>
                <div class="score-label">整合度得分</div>
                <div class="score-level" :style="{ color: getScoreColor(currentScore) }">
                  {{ getScoreLevel(currentScore) }}
                </div>
              </div>
            </el-card>
          </div>

          <!-- 整合度排名表格 -->
          <div class="ranking-table">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>重庆市各区县整合度排名</span>
                </div>
              </template>
              
              <el-table 
                :data="rankingData" 
                style="width: 100%" 
                height="300"
                :show-header="true"
              >
                <el-table-column prop="rank" label="排名" width="80" align="center">
                  <template #default="{ row }">
                    <div class="rank-cell">
                      <span>{{ row.rank }}</span>
                      <el-icon v-if="row.isFirst" class="first-icon">
                        <Star />
                      </el-icon>
                      <el-icon v-if="row.isLast" class="last-icon">
                        <Warning />
                      </el-icon>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="district" label="区县" width="120" />
                <el-table-column prop="score" label="得分" width="80" align="center">
                  <template #default="{ row }">
                    <span :style="{ color: getScoreColor(row.score), fontWeight: 'bold' }">
                      {{ row.score }}分
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="等级" width="100" align="center">
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.score >= 80 ? 'success' : row.score >= 60 ? 'warning' : 'danger'"
                      size="small"
                    >
                      {{ getScoreLevel(row.score) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </div>

        <!-- 第二行：趋势图和对比图 -->
        <div class="bottom-row">
          <!-- 整合度趋势曲线图 -->
          <div class="trend-chart">
            <el-card>
              <div ref="trendChartRef" style="width: 100%; height: 350px;"></div>
            </el-card>
          </div>

          <!-- 整合度对比折线图 -->
          <div class="comparison-chart">
            <el-card>
              <template #header>
                <div class="comparison-header">
                  <span>整合度对比分析</span>
                  <div class="comparison-selectors">
                    <el-select 
                      v-model="comparisonForm.district1" 
                      placeholder="选择地区1"
                      size="small"
                      style="width: 100px; margin-right: 8px"
                      @change="handleComparisonChange"
                    >
                      <el-option
                        v-for="district in chongqingDistricts"
                        :key="district"
                        :label="district"
                        :value="district"
                      />
                    </el-select>
                    <span style="margin: 0 8px;">VS</span>
                    <el-select 
                      v-model="comparisonForm.district2" 
                      placeholder="选择地区2"
                      size="small"
                      style="width: 100px"
                      @change="handleComparisonChange"
                    >
                      <el-option
                        v-for="district in chongqingDistricts"
                        :key="district"
                        :label="district"
                        :value="district"
                      />
                    </el-select>
                  </div>
                </div>
              </template>
              
              <div ref="comparisonChartRef" style="width: 100%; height: 300px;"></div>
            </el-card>
          </div>
        </div>
      </div>
    </Block>

    <!-- 生成整合度报告对话框 -->
    <Dialog
      v-model="showReportDialog"
      title="整合度报告操作"
      width="500px"
      :enable-confirm="false"
      cancel-text="关闭"
    >
      <div class="report-actions">
        <div class="action-grid">
          <div
            v-for="action in reportActions"
            :key="action.action"
            class="action-item"
            @click="handleReportAction(action.action)"
          >
            <el-icon class="action-icon">
              <component :is="action.icon" />
            </el-icon>
            <span class="action-label">{{ action.label }}</span>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 报告预览对话框 -->
    <Dialog
      v-model="showReportPreviewDialog"
      title="报表整合度分析报告"
      width="1200px"
      :enable-confirm="false"
      cancel-text="关闭"
      :loading="reportPreviewLoading"
    >
      <div class="report-preview" id="report-content">
        <!-- 报告标题和基本信息 -->
        <div class="report-header">
          <h1 class="report-title">重庆市报表整合度分析报告</h1>
          <div class="report-meta">
            <p><strong>生成时间：</strong>{{ new Date().toLocaleString('zh-CN') }}</p>
            <p><strong>报告范围：</strong>重庆市各区县</p>
            <p><strong>分析周期：</strong>最近12个月</p>
          </div>
        </div>

        <!-- 整合度得分概览 -->
        <div class="report-section">
          <h2 class="section-title">一、整合度得分概览</h2>
          <div class="score-overview">
            <div class="score-summary">
              <div class="current-score-display">
                <span class="score-value" :style="{ color: getScoreColor(currentScore) }">
                  {{ currentScore }}分
                </span>
                <span class="score-description">
                  当前重庆市整体报表整合度得分为{{ currentScore }}分，
                  评级为<span :style="{ color: getScoreColor(currentScore) }">{{ getScoreLevel(currentScore) }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 区县排名详情 -->
        <div class="report-section">
          <h2 class="section-title">二、各区县整合度排名</h2>
          <div class="ranking-summary">
            <p>本次分析涵盖重庆市{{ rankingData.length }}个区县，按整合度得分降序排列如下：</p>
            <div class="ranking-table-container">
              <table class="report-table">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>区县名称</th>
                    <th>整合度得分</th>
                    <th>评级</th>
                    <th>备注</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="item in rankingData" :key="item.id">
                    <td>{{ item.rank }}</td>
                    <td>{{ item.district }}</td>
                    <td :style="{ color: getScoreColor(item.score), fontWeight: 'bold' }">
                      {{ item.score }}分
                    </td>
                    <td>
                      <span
                        class="level-tag"
                        :class="{
                          'level-excellent': item.score >= 80,
                          'level-good': item.score >= 60 && item.score < 80,
                          'level-poor': item.score < 60
                        }"
                      >
                        {{ getScoreLevel(item.score) }}
                      </span>
                    </td>
                    <td>
                      <span v-if="item.isFirst" class="remark excellent">🏆 优秀示范</span>
                      <span v-else-if="item.isLast" class="remark warning">⚠️ 待改进</span>
                      <span v-else>-</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 趋势分析摘要 -->
        <div class="report-section">
          <h2 class="section-title">三、整合度趋势分析</h2>
          <div class="trend-summary">
            <p>基于最近12个月的数据分析，重庆市报表整合度呈现以下趋势特点：</p>
            <ul class="trend-points">
              <li>整体趋势：整合度水平保持相对稳定，波动范围在合理区间内</li>
              <li>季节性特征：各季度间存在一定差异，需持续关注低分预警情况</li>
              <li>改进建议：建议加强对得分较低区县的技术指导和支持</li>
            </ul>
          </div>
        </div>

        <!-- 对比分析结果 -->
        <div class="report-section">
          <h2 class="section-title">四、区县对比分析</h2>
          <div class="comparison-summary">
            <p>以{{ comparisonForm.district1 }}和{{ comparisonForm.district2 }}为例进行对比分析：</p>
            <div class="comparison-details">
              <div class="comparison-item">
                <strong>{{ comparisonForm.district1 }}：</strong>
                <span>在对比期间表现稳定，整合度水平处于{{ getScoreLevel(Math.floor(Math.random() * 45) + 50) }}水平</span>
              </div>
              <div class="comparison-item">
                <strong>{{ comparisonForm.district2 }}：</strong>
                <span>整合度发展态势良好，与{{ comparisonForm.district1 }}形成良性竞争关系</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 报告结论 -->
        <div class="report-section">
          <h2 class="section-title">五、总结与建议</h2>
          <div class="conclusion">
            <p><strong>总体评价：</strong>重庆市报表整合度整体水平{{ getScoreLevel(currentScore) }}，各区县发展相对均衡。</p>
            <p><strong>主要建议：</strong></p>
            <ul class="suggestions">
              <li>继续加强优秀区县的示范引领作用</li>
              <li>重点关注和帮扶整合度较低的区县</li>
              <li>建立定期监测和评估机制</li>
              <li>促进区县间经验交流与合作</li>
            </ul>
          </div>
        </div>

        <!-- 报告尾注 -->
        <div class="report-footer">
          <p class="footer-text">
            本报告由重庆市报表整合度抽查系统自动生成，数据截止时间：{{ new Date().toLocaleDateString('zh-CN') }}
          </p>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.result-display {
  height: 100%;

  .result-content {
    padding: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .top-row {
      display: flex;
      gap: 16px;
      height: 350px;

      .score-card {
        flex: 0 0 300px;

        .score-display {
          height: 100%;

          :deep(.el-card__body) {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
          }

          .score-content {
            text-align: center;

            .score-number {
              font-size: 72px;
              font-weight: bold;
              line-height: 1;
              margin-bottom: 8px;
            }

            .score-label {
              font-size: 16px;
              color: #606266;
              margin-bottom: 8px;
            }

            .score-level {
              font-size: 18px;
              font-weight: bold;
            }
          }
        }
      }

      .ranking-table {
        flex: 1;

        .card-header {
          font-weight: bold;
          font-size: 16px;
        }

        .rank-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;

          .first-icon {
            color: #F7BA2A;
            font-size: 16px;
          }

          .last-icon {
            color: #F56C6C;
            font-size: 16px;
          }
        }
      }
    }

    .bottom-row {
      display: flex;
      gap: 16px;
      flex: 1;
      min-height: 400px;

      .trend-chart {
        flex: 1;
      }

      .comparison-chart {
        flex: 1;

        .comparison-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-weight: bold;
          font-size: 16px;

          .comparison-selectors {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: normal;
          }
        }
      }
    }
  }
}

.report-actions {
  .action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 16px 0;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      border: 1px solid #DCDFE6;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409EFF;
        background-color: #F0F9FF;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }

      .action-icon {
        font-size: 32px;
        color: #409EFF;
        margin-bottom: 8px;
      }

      .action-label {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

// 报告预览样式
.report-preview {
  padding: 20px;
  background: #fff;
  color: #333;
  line-height: 1.6;
  font-family: 'Microsoft YaHei', Arial, sans-serif;

  .report-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #409EFF;

    .report-title {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin: 0 0 15px 0;
    }

    .report-meta {
      font-size: 14px;
      color: #606266;

      p {
        margin: 5px 0;
      }
    }
  }

  .report-section {
    margin-bottom: 25px;

    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      margin: 0 0 15px 0;
      padding-left: 10px;
      border-left: 4px solid #409EFF;
    }
  }

  .score-overview {
    .score-summary {
      background: #F8F9FA;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #E9ECEF;

      .current-score-display {
        text-align: center;

        .score-value {
          font-size: 48px;
          font-weight: bold;
          display: block;
          margin-bottom: 10px;
        }

        .score-description {
          font-size: 16px;
          color: #606266;
        }
      }
    }
  }

  .ranking-summary {
    p {
      margin-bottom: 15px;
      color: #606266;
    }

    .ranking-table-container {
      overflow-x: auto;
    }
  }

  .report-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    font-size: 14px;

    th, td {
      padding: 12px 8px;
      text-align: center;
      border: 1px solid #DCDFE6;
    }

    th {
      background-color: #F5F7FA;
      font-weight: bold;
      color: #303133;
    }

    tbody tr:nth-child(even) {
      background-color: #FAFAFA;
    }

    .level-tag {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.level-excellent {
        background-color: #F0F9FF;
        color: #67C23A;
        border: 1px solid #67C23A;
      }

      &.level-good {
        background-color: #FDF6EC;
        color: #E6A23C;
        border: 1px solid #E6A23C;
      }

      &.level-poor {
        background-color: #FEF0F0;
        color: #F56C6C;
        border: 1px solid #F56C6C;
      }
    }

    .remark {
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 4px;

      &.excellent {
        background-color: #F0F9FF;
        color: #409EFF;
      }

      &.warning {
        background-color: #FDF6EC;
        color: #E6A23C;
      }
    }
  }

  .trend-summary, .comparison-summary, .conclusion {
    p {
      margin-bottom: 10px;
      color: #606266;
    }

    ul {
      margin: 10px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: #606266;
      }
    }
  }

  .comparison-details {
    background: #F8F9FA;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;

    .comparison-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .suggestions {
    background: #F0F9FF;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #409EFF;
  }

  .report-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #DCDFE6;
    text-align: center;

    .footer-text {
      font-size: 12px;
      color: #909399;
      margin: 0;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .result-content {
    .top-row {
      flex-direction: column;
      height: auto;

      .score-card {
        flex: none;

        .score-display {
          height: 200px;
        }
      }

      .ranking-table {
        flex: none;
      }
    }

    .bottom-row {
      flex-direction: column;
      min-height: auto;

      .trend-chart,
      .comparison-chart {
        flex: none;
      }
    }
  }
}

@media (max-width: 768px) {
  .result-content {
    padding: 8px;
    gap: 8px;

    .top-row .score-card .score-display .score-content .score-number {
      font-size: 48px;
    }

    .bottom-row .comparison-chart .comparison-header {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
    }
  }

  .report-actions .action-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

// Element Plus 样式覆盖
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #EBEEF5;
  }

  .el-card__body {
    padding: 20px;
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #F5F7FA;
        color: #303133;
        font-weight: bold;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__row {
      &:hover {
        background-color: #F5F7FA;
      }
    }
  }
}

:deep(.el-select) {
  .el-input__inner {
    border-radius: 4px;
  }
}

:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}
</style>
