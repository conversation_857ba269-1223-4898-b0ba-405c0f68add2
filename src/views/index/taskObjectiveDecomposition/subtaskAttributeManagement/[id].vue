<template>
  <div class="subtask-attribute-management" v-loading="loading">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">子任务属性</h2>
        <span class="page-subtitle" v-if="taskDetail">{{ taskDetail.taskName }}</span>
      </div>
      <div class="header-actions">
        <el-button @click="handleReturn" class="return-button">返回</el-button>
      </div>
    </div>
    
    <!-- Navigation Buttons -->
    <div class="navigation-section">
      <el-button
        type="success"
        @click="handleAddAttribute"
        class="add-attribute-button"
        icon="Plus"
      >
        新增子任务属性
      </el-button>
      <el-button
        type="primary"
        @click="handleAttributeTypeManagement"
        class="attribute-type-button"
        icon="Setting"
      >
        属性类型管理
      </el-button>
      <el-button
        type="warning"
        @click="handleAttributeVisualization"
        class="attribute-visualization-button"
        icon="View"
      >
        属性可视化
      </el-button>

      <!-- 属性值计算下拉菜单 -->
      <el-dropdown @command="handleAttributeCalculation" class="attribute-calculation-dropdown">
        <el-button type="info" icon="DataAnalysis">
          属性值计算
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="max">属性值最大值计算</el-dropdown-item>
            <el-dropdown-item command="min">属性值最小值计算</el-dropdown-item>
            <el-dropdown-item command="avg">属性值平均值计算</el-dropdown-item>
            <el-dropdown-item command="count">属性值数量统计</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 新增的5个功能按钮 -->
      <el-button
        type="primary"
        @click="handleAttributeStatistics"
        class="attribute-statistics-button"
        icon="DataBoard"
      >
        属性值统计
      </el-button>
      <el-button
        type="success"
        @click="handlePermissionSettings"
        class="permission-settings-button"
        icon="User"
      >
        权限设置
      </el-button>
      <el-button
        type="warning"
        @click="handleUrgencyRules"
        class="urgency-rules-button"
        icon="Timer"
      >
        紧急程度规则
      </el-button>
      <el-button
        type="danger"
        @click="handleRiskLevelManagement"
        class="risk-level-button"
        icon="Warning"
      >
        风险等级管理
      </el-button>
      <el-button
        type="info"
        @click="handleHistoryRecord"
        class="history-record-button"
        icon="Document"
      >
        历史记录
      </el-button>
    </div>
    
    <!-- Search Section -->
    <div class="search-section">
      <div class="search-form">
        <el-input
          v-model="searchForm.keyword"
          placeholder="请输入子任务属性名称"
          class="search-input"
          clearable
          @clear="handleSearch"
        />
        <el-select
          v-model="searchForm.taskType"
          placeholder="请选择任务类型"
          class="search-select"
          clearable
        >
          <el-option
            v-for="type in filterOptions.taskTypes"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>
        <el-select
          v-model="searchForm.attributeType"
          placeholder="请选择属性类型"
          class="search-select"
          clearable
        >
          <el-option
            v-for="type in filterOptions.attributeTypes"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </div>
    
    <!-- Data Table -->
    <div class="table-section">
      <BaseTableComp
        :data="filteredData"
        :colData="tableColumns"
        :buttons="operationButtons"
        :loading="tableLoading"
        :checkbox="true"
        :visibleHeader="false"
        :visibleSetting="false"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="true"
        style="height: 500px;"
        @clickButton="handleOperationClick"
        @currentChange="handlePageChange"
        @selectionChange="handleSelectionChange"
      >
        <template #urgencyLevel="{ rowData }">
          <el-tag :type="getUrgencyType(rowData.urgencyLevel)">
            {{ rowData.urgencyLevel }}
          </el-tag>
        </template>
        
        <template #riskLevel="{ rowData }">
          <el-tag :type="getRiskType(rowData.riskLevel)">
            {{ rowData.riskLevel }}
          </el-tag>
        </template>
        
        <template #importanceLevel="{ rowData }">
          <el-tag :type="getImportanceType(rowData.importanceLevel)">
            {{ rowData.importanceLevel }}
          </el-tag>
        </template>
        
        <template #status="{ rowData }">
          <el-tag :type="getStatusType(rowData.status)">
            {{ rowData.status }}
          </el-tag>
        </template>
      </BaseTableComp>
    </div>

    <!-- 新增子任务属性弹窗 -->
    <SubtaskAttributeFormDialog
      v-model="showAddDialog"
      title="新增子任务属性"
      mode="add"
      :attribute-types="filterOptions.attributeTypes"
      :initial-data="addForm"
      @confirm="handleConfirmAdd"
      @cancel="handleCancelAdd"
      @copy-value="handleCopyValue"
      @generate-value="handleGenerateValue"
    />

    <!-- 子任务属性调整弹窗 -->
    <SubtaskAttributeFormDialog
      v-model="showAdjustDialog"
      title="子任务属性调整"
      mode="edit"
      :loading="adjustLoading"
      :attribute-types="filterOptions.attributeTypes"
      :initial-data="adjustForm"
      @confirm="handleConfirmAdjust"
      @cancel="handleCancelAdjust"
      @copy-value="handleCopyValue"
      @generate-value="handleGenerateValue"
    />

    <!-- 属性类型管理弹窗 -->
    <AttributeTypeManagementDialog
      v-model="showAttributeTypeDialog"
      @confirm="handleAttributeTypeConfirm"
    />

    <!-- 属性可视化弹窗 -->
    <AttributeVisualizationDialog
      v-model="showAttributeVisualizationDialog"
      @confirm="handleAttributeVisualizationConfirm"
    />

    <!-- 属性值计算结果弹窗 -->
    <DialogComp
      v-model="showCalculationResultDialog"
      :title="calculationResult.title"
      width="500px"
      :visible-footer-button="false"
      :close-on-click-modal="true"
      @close="handleCloseCalculationResult"
    >
      <div class="calculation-result-content">
        <div class="result-item">
          <span class="label">计算类型：</span>
          <span class="value">{{ calculationResult.type }}</span>
        </div>
        <div class="result-item">
          <span class="label">计算结果：</span>
          <span class="value result-number">{{ calculationResult.value }}</span>
        </div>
        <div class="result-item">
          <span class="label">参与计算的数据条数：</span>
          <span class="value">{{ calculationResult.count }}</span>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseCalculationResult">关闭</el-button>
        </div>
      </template>
    </DialogComp>

    <!-- 属性值统计弹窗 -->
    <AttributeStatisticsDialog
      v-model="showAttributeStatisticsDialog"
      @confirm="handleAttributeStatisticsConfirm"
      @cancel="handleAttributeStatisticsCancel"
    />

    <!-- 权限设置弹窗 -->
    <PermissionSettingsDialog
      v-model="showPermissionSettingsDialog"
      @confirm="handlePermissionSettingsConfirm"
      @cancel="handlePermissionSettingsCancel"
    />

    <!-- 紧急程度规则弹窗 -->
    <UrgencyRulesDialog
      v-model="showUrgencyRulesDialog"
      @confirm="handleUrgencyRulesConfirm"
      @cancel="handleUrgencyRulesCancel"
    />

    <!-- 风险等级管理弹窗 -->
    <RiskLevelManagementDialog
      v-model="showRiskLevelManagementDialog"
      @confirm="handleRiskLevelManagementConfirm"
      @cancel="handleRiskLevelManagementCancel"
    />

    <!-- 历史记录弹窗 -->
    <HistoryRecordDialog
      v-model="showHistoryRecordDialog"
      @confirm="handleHistoryRecordConfirm"
      @cancel="handleHistoryRecordCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import DialogComp from '@/components/common/dialog-comp.vue'
import AttributeTypeManagementDialog from './components/AttributeTypeManagementDialog.vue'
import AttributeVisualizationDialog from './components/AttributeVisualizationDialog.vue'
import SubtaskAttributeFormDialog from './components/SubtaskAttributeFormDialog.vue'
import AttributeStatisticsDialog from './components/AttributeStatisticsDialog.vue'
import PermissionSettingsDialog from './components/PermissionSettingsDialog.vue'
import UrgencyRulesDialog from './components/UrgencyRulesDialog.vue'
import RiskLevelManagementDialog from './components/RiskLevelManagementDialog.vue'
import HistoryRecordDialog from './components/HistoryRecordDialog.vue'
import type { SubtaskAttribute, SubtaskAttributeQuery } from '@/define/subtaskAttribute.define'
import {
  mockSubtaskAttributes,
  operationButtons,
  tableColumns,
  filterOptions
} from './mockData'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const tableLoading = ref(false)
const taskDetail = ref<any>(null)

// 属性类型管理弹窗
const showAttributeTypeDialog = ref(false)

// 属性可视化弹窗
const showAttributeVisualizationDialog = ref(false)

// 属性值计算结果弹窗
const showCalculationResultDialog = ref(false)
const calculationResult = ref({
  title: '',
  type: '',
  value: '',
  count: 0
})

// 新增的5个弹窗状态
const showAttributeStatisticsDialog = ref(false)
const showPermissionSettingsDialog = ref(false)
const showUrgencyRulesDialog = ref(false)
const showRiskLevelManagementDialog = ref(false)
const showHistoryRecordDialog = ref(false)

// 搜索表单
const searchForm = ref<SubtaskAttributeQuery>({
  keyword: '',
  taskType: '',
  attributeType: ''
})

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const subtaskData = ref<SubtaskAttribute[]>([])
const selectedRows = ref<SubtaskAttribute[]>([])

// 新增弹窗相关数据
const showAddDialog = ref(false)
const addForm = ref({
  attributeName: '',
  taskType: '',
  attributeType: '',
  attributeValue: '',
  urgencyLevel: '',
  riskLevel: '',
  importanceLevel: '',
  riskWarningEnabled: false,
  riskWarningValue: 0,
  reminderFrequency: ''
})

// 调整弹窗相关数据
const showAdjustDialog = ref(false)
const currentAdjustRow = ref<SubtaskAttribute | null>(null)
const adjustLoading = ref(false)
const adjustForm = ref({
  attributeName: '',
  taskType: '',
  attributeType: '',
  attributeValue: '',
  urgencyLevel: '',
  riskLevel: '',
  importanceLevel: '',
  riskWarningEnabled: false,
  riskWarningValue: 0,
  reminderFrequency: ''
})

// 过滤后的数据
const filteredData = computed(() => {
  let data = subtaskData.value
  
  if (searchForm.value.keyword) {
    data = data.filter(item => 
      item.attributeName.toLowerCase().includes(searchForm.value.keyword!.toLowerCase())
    )
  }
  
  if (searchForm.value.taskType) {
    data = data.filter(item => item.taskType === searchForm.value.taskType)
  }
  
  if (searchForm.value.attributeType) {
    data = data.filter(item => item.attributeType === searchForm.value.attributeType)
  }
  
  return data
})

// 标签类型获取函数
const getUrgencyType = (urgencyLevel: string) => {
  const urgencyTypeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '特急': 'danger',
    '紧急': 'warning',
    '一般': 'primary',
    '不急': 'info'
  }
  return urgencyTypeMap[urgencyLevel] || 'info'
}

const getRiskType = (riskLevel: string) => {
  const riskTypeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '高风险': 'danger',
    '中风险': 'warning',
    '低风险': 'success'
  }
  return riskTypeMap[riskLevel] || 'info'
}

const getImportanceType = (importanceLevel: string) => {
  const importanceTypeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '非常重要': 'danger',
    '重要': 'warning',
    '一般': 'primary',
    '不重要': 'info'
  }
  return importanceTypeMap[importanceLevel] || 'info'
}

const getStatusType = (status: string) => {
  const statusTypeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '进行中': 'primary',
    '已完成': 'success',
    '待处理': 'warning',
    '已取消': 'info'
  }
  return statusTypeMap[status] || 'info'
}

// 事件处理函数
const handleReturn = () => {
  router.back()
}

const handleAddAttribute = () => {
  showAddDialog.value = true
}

const handleAttributeTypeManagement = () => {
  showAttributeTypeDialog.value = true
}

const handleAttributeTypeConfirm = (data: any) => {
  ElMessage.success('属性类型管理设置已保存')
  console.log('属性类型管理数据:', data)
}

const handleAttributeVisualization = () => {
  showAttributeVisualizationDialog.value = true
}

const handleAttributeVisualizationConfirm = (data: any) => {
  ElMessage.success('属性可视化设置已保存')
  console.log('属性可视化数据:', data)
}

// 属性值计算处理函数
const handleAttributeCalculation = (command: string) => {
  const data = filteredData.value
  const validData = data.filter(item =>
    typeof item.attributeValue === 'number' &&
    !isNaN(item.attributeValue)
  )

  if (validData.length === 0) {
    ElMessage.warning('当前数据中没有有效的属性值进行计算')
    return
  }

  const attributeValues = validData.map(item => item.attributeValue)
  let result: number | string = 0
  let calculationType = ''

  switch (command) {
    case 'max':
      result = Math.max(...attributeValues)
      calculationType = '属性值最大值计算'
      break
    case 'min':
      result = Math.min(...attributeValues)
      calculationType = '属性值最小值计算'
      break
    case 'avg':
      const sum = attributeValues.reduce((acc, val) => acc + val, 0)
      result = (sum / attributeValues.length).toFixed(2)
      calculationType = '属性值平均值计算'
      break
    case 'count':
      result = validData.length
      calculationType = '属性值数量统计'
      break
    default:
      ElMessage.error('未知的计算类型')
      return
  }

  calculationResult.value = {
    title: '属性值计算结果',
    type: calculationType,
    value: result.toString(),
    count: validData.length
  }

  showCalculationResultDialog.value = true
}

// 关闭计算结果弹窗
const handleCloseCalculationResult = () => {
  showCalculationResultDialog.value = false
}

const handleSearch = () => {
  ElMessage.success(`搜索完成，找到 ${filteredData.value.length} 条记录`)
}

const handleReset = () => {
  searchForm.value = {
    keyword: '',
    taskType: '',
    attributeType: ''
  }
  ElMessage.info('已重置搜索条件')
}

const handleOperationClick = (data: any) => {
  const { btn, scope } = data
  const row = scope.row

  switch (btn.code) {
    case 'adjust':
      handleOpenAdjustDialog(row)
      break
    case 'submit':
      ElMessage.info(`提交子任务属性: ${row.attributeName}`)
      break
    case 'audit':
      ElMessage.info(`审核子任务属性: ${row.attributeName}`)
      break
    case 'more':
      ElMessage.info(`属性关联操作: ${row.attributeName}`)
      break
    default:
      ElMessage.info(`执行操作: ${btn.title}`)
  }
}

const handlePageChange = (page: number) => {
  pagination.value.currentPage = page
}

const handleSelectionChange = (selection: SubtaskAttribute[]) => {
  selectedRows.value = selection
}

// 新增弹窗处理函数
const handleCopyValue = () => {
  if (addForm.value.attributeValue) {
    navigator.clipboard.writeText(addForm.value.attributeValue.toString())
    ElMessage.success('属性值复制成功')
  } else {
    ElMessage.warning('请先输入属性值')
  }
}

const handleGenerateValue = () => {
  // 随机生成1-10的值
  const randomValue = Math.floor(Math.random() * 10) + 1
  addForm.value.attributeValue = randomValue.toString()
  ElMessage.success(`已生成属性值: ${randomValue}`)
}

const handleCancelAdd = () => {
  showAddDialog.value = false

  // 重置表单
  addForm.value = {
    attributeName: '',
    taskType: '',
    attributeType: '',
    attributeValue: '',
    urgencyLevel: '',
    riskLevel: '',
    importanceLevel: '',
    riskWarningEnabled: false,
    riskWarningValue: 0,
    reminderFrequency: ''
  }
}

// 调整弹窗处理函数
const handleOpenAdjustDialog = (row: SubtaskAttribute) => {
  try {
    currentAdjustRow.value = row

    // 预填充表单数据
    adjustForm.value = {
      attributeName: row.attributeName || '',
      taskType: row.taskType || '',
      attributeType: row.attributeType || '',
      attributeValue: (row.attributeValue || 1).toString(),
      urgencyLevel: row.urgencyLevel || '',
      riskLevel: row.riskLevel || '',
      importanceLevel: row.importanceLevel || '',
      riskWarningEnabled: false, // 默认值，实际项目中应从数据中获取
      riskWarningValue: 5, // 默认值，实际项目中应从数据中获取
      reminderFrequency: '每天' // 默认值，实际项目中应从数据中获取
    }

    showAdjustDialog.value = true
  } catch (error) {
    console.error('打开调整弹窗失败:', error)
    ElMessage.error('打开调整弹窗失败')
  }
}

const handleCancelAdjust = () => {
  showAdjustDialog.value = false
  currentAdjustRow.value = null

  // 重置表单
  adjustForm.value = {
    attributeName: '',
    taskType: '',
    attributeType: '',
    attributeValue: '',
    urgencyLevel: '',
    riskLevel: '',
    importanceLevel: '',
    riskWarningEnabled: false,
    riskWarningValue: 0,
    reminderFrequency: ''
  }
}

const handleConfirmAdjust = async (formData: any) => {
  if (!currentAdjustRow.value) return

  adjustLoading.value = true

  try {

    // 模拟异步保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新当前行的数据
    const updatedAttribute: SubtaskAttribute = {
      ...currentAdjustRow.value,
      attributeName: formData.attributeName,
      taskType: formData.taskType,
      attributeType: formData.attributeType,
      attributeValue: parseInt(formData.attributeValue) || 1,
      urgencyLevel: formData.urgencyLevel,
      riskLevel: formData.riskLevel,
      importanceLevel: formData.importanceLevel,
      lastModified: new Date().toISOString().split('T')[0],
      modifier: '当前用户'
    }

    // 在数据列表中找到并更新对应的记录
    const index = subtaskData.value.findIndex(item => item.id === currentAdjustRow.value!.id)
    if (index !== -1) {
      subtaskData.value[index] = updatedAttribute
    }

    ElMessage.success('子任务属性调整成功')
    handleCancelAdjust()
  } catch (error) {
    console.error('调整失败:', error)
    if (error instanceof Error) {
      ElMessage.error(`调整失败: ${error.message}`)
    } else {
      ElMessage.error('请检查表单输入')
    }
  } finally {
    adjustLoading.value = false
  }
}

const handleConfirmAdd = async (formData: any) => {
  try {

    // 创建新的子任务属性
    const newAttribute: SubtaskAttribute = {
      id: `attr_${Date.now()}`,
      attributeName: formData.attributeName,
      taskType: formData.taskType,
      attributeType: formData.attributeType,
      attributeValue: parseInt(formData.attributeValue) || 1,
      urgencyLevel: formData.urgencyLevel,
      riskLevel: formData.riskLevel,
      importanceLevel: formData.importanceLevel,
      status: '待处理',
      lastModified: new Date().toISOString().split('T')[0],
      modifier: '当前用户'
    }

    // 添加到数据列表
    subtaskData.value.unshift(newAttribute)
    pagination.value.total = subtaskData.value.length

    ElMessage.success('新增子任务属性成功')
    handleCancelAdd()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 初始化数据
const initializeData = () => {
  loading.value = true
  
  try {
    // 模拟加载任务详情
    const taskId = route.params.id
    taskDetail.value = {
      taskName: `任务目标拆解详情 - ${taskId}`,
      taskType: '业务报表任务'
    }
    
    // 加载子任务属性数据
    subtaskData.value = mockSubtaskAttributes
    pagination.value.total = subtaskData.value.length
    
    // 处理从详情页传递的选中子任务
    const selectedSubtasks = route.query.selectedSubtasks as string
    if (selectedSubtasks) {
      const selectedIds = selectedSubtasks.split(',')
      ElMessage.success(`已接收到 ${selectedIds.length} 个选中的子任务`)
    }
    
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 新增的5个弹窗事件处理函数
const handleAttributeStatistics = () => {
  showAttributeStatisticsDialog.value = true
}

const handleAttributeStatisticsConfirm = (data: any) => {
  ElMessage.success('属性值统计确认成功')
  console.log('属性值统计数据:', data)
}

const handleAttributeStatisticsCancel = () => {
  ElMessage.info('已取消属性值统计')
}

const handlePermissionSettings = () => {
  showPermissionSettingsDialog.value = true
}

const handlePermissionSettingsConfirm = (data: any) => {
  ElMessage.success('权限设置保存成功')
  console.log('权限设置数据:', data)
}

const handlePermissionSettingsCancel = () => {
  ElMessage.info('已取消权限设置')
}

const handleUrgencyRules = () => {
  showUrgencyRulesDialog.value = true
}

const handleUrgencyRulesConfirm = (data: any) => {
  ElMessage.success('紧急程度规则确认成功')
  console.log('紧急程度规则数据:', data)
}

const handleUrgencyRulesCancel = () => {
  ElMessage.info('已取消紧急程度规则')
}

const handleRiskLevelManagement = () => {
  showRiskLevelManagementDialog.value = true
}

const handleRiskLevelManagementConfirm = (data: any) => {
  ElMessage.success('风险等级管理确认成功')
  console.log('风险等级管理数据:', data)
}

const handleRiskLevelManagementCancel = () => {
  ElMessage.info('已取消风险等级管理')
}

const handleHistoryRecord = () => {
  showHistoryRecordDialog.value = true
}

const handleHistoryRecordConfirm = (data: any) => {
  ElMessage.success('历史记录确认成功')
  console.log('历史记录数据:', data)
}

const handleHistoryRecordCancel = () => {
  ElMessage.info('已取消历史记录查看')
}

onMounted(() => {
  initializeData()
})
</script>

<style scoped lang="scss">
.subtask-attribute-management {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }
      
      .page-subtitle {
        font-size: 14px;
        color: #909399;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .navigation-section {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
    justify-content: flex-start;

    .add-attribute-button,
    .attribute-type-button,
    .attribute-visualization-button,
    .attribute-statistics-button,
    .permission-settings-button,
    .urgency-rules-button,
    .risk-level-button,
    .history-record-button {
      border-radius: 4px;
      font-size: 14px;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .attribute-calculation-dropdown {
      .el-button {
        border-radius: 4px;
        font-size: 14px;
        padding: 8px 16px;
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
  
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .search-form {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-wrap: wrap;
      
      .search-input {
        width: 200px;
      }
      
      .search-select {
        width: 160px;
      }
    }
  }
  
  .table-section {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 弹窗样式
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.add-form {
  .el-form-item {
    margin-bottom: 20px;
    
    .el-form-item__label {
      color: #303133;
      font-weight: 500;
      
      &::before {
        content: "*";
        color: #f56c6c;
        margin-right: 4px;
      }
    }
    
    &:not([required]) .el-form-item__label::before {
      display: none;
    }
  }
  
  .attribute-value-section {
    width: 100%;
    
    .value-hint {
      background: #f0f9ff;
      color: #1f2937;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      margin-bottom: 12px;
      border-left: 3px solid #409eff;
    }
    
    .value-input-group {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }
  
  .risk-warning-section {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .warning-value-section {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .warning-label,
    .warning-suffix {
      color: #606266;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 10px 0;
}

// 计算结果弹窗样式
.calculation-result-content {
  padding: 20px 0;

  .result-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-weight: 500;
      color: #303133;
      min-width: 140px;
      margin-right: 12px;
    }

    .value {
      color: #606266;

      &.result-number {
        font-size: 18px;
        font-weight: 600;
        color: #409eff;
      }
    }
  }
}
</style>