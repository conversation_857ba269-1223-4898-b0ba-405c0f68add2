<!-- 任务目标拆解详情页面 -->
<script setup lang="ts" name="TaskObjectiveDecompositionDetail">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import DialogComp from '@/components/common/dialog-comp.vue'
import ProgressConfigDialog from '../components/ProgressConfigDialog.vue'
import DifficultyAnalysisDialog from '../components/DifficultyAnalysisDialog.vue'
import SubTaskEditDialog from '../components/SubTaskEditDialog.vue'
import ReminderHistoryDialog from '../components/ReminderHistoryDialog.vue'
import ReminderSettingDialog from '../components/ReminderSettingDialog.vue'
import RiskSettingDialog from '../components/RiskSettingDialog.vue'
import PropertyManagementDialog from '../components/PropertyManagementDialog.vue'
import SubtaskRelationDialog from '@/components/SubtaskRelationDialog.vue'
import UrgencyAdjustmentDialog from '../components/UrgencyAdjustmentDialog.vue'
import ImportanceAdjustmentDialog from '../components/ImportanceAdjustmentDialog.vue'
import SubTaskResourceDialog from '../components/SubTaskResourceDialog.vue'
import SubTaskSplitDialog from '../components/SubTaskSplitDialog.vue'
import RiskAnalysisDialog from '@/components/RiskAnalysisDialog.vue'
import SubTaskHistoryDialog from '@/components/SubTaskHistoryDialog.vue'

// Composables
import { useTaskDetailState } from '../composables/useTaskDetailState'
import { useSubTaskOperations } from '../composables/useSubTaskOperations'
import { useBatchOperations } from '../composables/useBatchOperations'

// Config and Utils
import { subTaskColumns, subTaskButtons, statusColorMap } from '../config/tableConfig'
import { exportSubTaskList } from '../utils/exportUtils'
import { generateTimeAnalysisData, progressTrackingData } from '../data/mockData'

// 使用状态管理
const {
  loading,
  taskId,
  taskDetail,
  dialogStates,
  loadingStates,
  currentEditData,
  searchForm,
  pagination,
  selectedRows,
  batchOperationData,
  riskAnalysisData,
  subTaskData,
  taskStatistics,
  loadSubTasks,
  loadTaskDetail,
  resetSearch,
  searchSubTasks,
  taskStore
} = useTaskDetailState()

// 使用子任务操作
const {
  canMoveUp,
  canMoveDown,
  handleDetailSubTask,
  handleAssociateSubTask,
  handleDeleteSubTask,
  handleCopySubTask,
  handleSubmitSubTask,
  handleAuditSubTask,
  handleMoveUp,
  handleMoveDown,
  handleProgressTracking,
  handleSubTaskEditConfirm,
  handleSubTaskSplit: handleSubTaskSplitOperation
} = useSubTaskOperations()

// 使用批量操作
const {
  handleBatchDelete,
  handleBatchCopy
} = useBatchOperations()

const router = useRouter()
const route = useRoute()

// 组件引用
const batchDialogsRef = ref()

// 时长分析数据
const timeAnalysisData = ref(generateTimeAnalysisData(taskDetail.value))

// 进度配置状态
const progressConfig = ref({
  progressCalculationEnabled: true
})

// 将函数挂载到window对象，以便在verify表达式中使用
;(window as any).canMoveUp = (row: any) => canMoveUp(row, subTaskData.value, taskId.value)
;(window as any).canMoveDown = (row: any) => canMoveDown(row, subTaskData.value, taskId.value)

// 加载进度配置
const loadProgressConfig = () => {
  const savedConfig = localStorage.getItem('progressConfig')
  if (savedConfig) {
    const config = JSON.parse(savedConfig)
    progressConfig.value.progressCalculationEnabled = config.progressCalculationEnabled ?? true
  }
}

// 格式化进度显示的函数
const formatProgress = (progress: number) => {
  if (progressConfig.value.progressCalculationEnabled) {
    // 开启百分比计算时，显示百分比
    return `${progress || 0}%`
  } else {
    // 关闭百分比计算时，显示具体数值（模拟完成数量/总数量）
    const total = 10 // 模拟总数量
    const completed = Math.floor((progress || 0) * total / 100) // 根据百分比计算完成数量
    return `${completed}/${total}`
  }
}

// 获取状态对应的标签类型
const getStatusType = (status: string) => {
  return statusColorMap[status as keyof typeof statusColorMap] || 'info'
}

// 获取紧急程度标签
const getUrgencyLabel = (urgencyLevel: string) => {
  const urgencyMap = {
    'P1': 'P1 - 特急',
    'P2': 'P2 - 加急',
    'P3': 'P3 - 平急',
    'P4': 'P4 - 不重要'
  }
  return urgencyMap[urgencyLevel as keyof typeof urgencyMap] || urgencyLevel
}

// 获取紧急程度标签类型
const getUrgencyType = (urgencyLevel: string) => {
  const urgencyColorMap = {
    'P1': 'danger' as const,
    'P2': 'warning' as const,
    'P3': 'primary' as const,
    'P4': 'info' as const
  }
  return urgencyColorMap[urgencyLevel as keyof typeof urgencyColorMap] || 'info'
}

// 生命周期
onMounted(async () => {
  // 确保Store数据已初始化
  taskStore.initializeData()
  // 加载进度配置
  loadProgressConfig()
  await loadTaskDetail()
})

// 组件卸载时清理window对象上的函数
onUnmounted(() => {
  delete (window as any).canMoveUp
  delete (window as any).canMoveDown
})

const handleBack = () => {
  // 返回到任务目标拆解列表页面，保持查询参数
  const currentQuery = route.query
  router.push({
    path: '/taskObjectiveDecomposition',
    query: currentQuery
  })
}

// 刷新子任务数据的回调函数
const refreshSubTasks = () => {
  const subTasks = loadSubTasks()
  pagination.total = subTasks.length
}

// 处理编辑子任务
const handleEditSubTask = (row: any) => {
  currentEditData.subTask = { ...row }
  dialogStates.subTaskEdit = true
}

// 处理设置提醒
const handleSetReminder = (row: any) => {
  currentEditData.reminderSubTask = { ...row }
  dialogStates.reminderSetting = true
}

// 处理进度追踪弹窗
const handleProgressTrackingDialog = (row: any) => {
  handleProgressTracking(row)
  // 这里可以设置进度追踪数据并显示弹窗
  dialogStates.progressTracking = true
}

// 处理风险设定
const handleRiskSetting = (row: any) => {
  currentEditData.riskSubTask = { ...row }
  dialogStates.riskSetting = true
}

// 处理属性管理
const handlePropertyManagement = (row: any) => {
  currentEditData.propertySubTask = { ...row }
  dialogStates.propertyManagement = true
}

// 处理子任务拆分
const handleSubTaskSplit = (row: any) => {
  currentEditData.splitSubTask = { ...row }
  dialogStates.subTaskSplit = true
}

const handleSubTaskButton = (button: any, row: any) => {
  switch (button.code) {
    case 'detail':
      handleDetailSubTask(row)
      break
    case 'associate':
      handleAssociateSubTask(row)
      break
    case 'delete':
      handleDeleteSubTask(row, refreshSubTasks)
      break
    case 'edit':
      handleEditSubTask(row)
      break
    case 'submit':
      handleSubmitSubTask(row, refreshSubTasks)
      break
    case 'audit':
      handleAuditSubTask(row, refreshSubTasks)
      break
    case 'copy':
      handleCopySubTask(row, refreshSubTasks)
      break
    case 'split':
      handleSubTaskSplit(row)
      break
    // 更多操作选项 - 子任务级别
    case 'moveUp':
      handleMoveUp(row, subTaskData.value, taskId.value, refreshSubTasks)
      break
    case 'moveDown':
      handleMoveDown(row, subTaskData.value, taskId.value, refreshSubTasks)
      break
    case 'setReminder':
      handleSetReminder(row)
      break
    case 'progressTracking':
      handleProgressTrackingDialog(row)
      break
    case 'riskSetting':
      handleRiskSetting(row)
      break
    case 'propertyManagement':
      handlePropertyManagement(row)
      break
    // 更多操作选项 - 任务级别（从详情栏移过来的）
    case 'taskDifficultyAnalysis':
      handleDifficultyAnalysis()
      break
    case 'taskTimeAnalysis':
      handleTimeAnalysis()
      break
    case 'taskReminderHistory':
      handleReminderHistory()
      break
    case 'editTask':
      handleEditTask()
      break
  }
}

// 其他弹窗处理函数
const handleDifficultyAnalysis = () => {
  dialogStates.difficultyAnalysis = true
}

const handleTimeAnalysis = () => {
  // 根据当前任务数据动态生成时长分析
  const task = taskDetail.value
  if (task) {
    timeAnalysisData.value = generateTimeAnalysisData(task)
  }
  dialogStates.timeAnalysis = true
}

const handleReminderHistory = () => {
  dialogStates.reminderHistory = true
}

const handleEditTask = () => {
  dialogStates.editTask = true
}

// 弹窗处理方法
const handleProgressConfig = () => {
  dialogStates.progressConfig = true
}

const handleStyleTemplate = () => {
  dialogStates.styleTemplate = true
}

// 风险分析处理
const handleRiskAnalysis = () => {
  riskAnalysisVisible.value = true
}

// 子任务历史处理
const handleSubTaskHistory = () => {
  subTaskHistoryVisible.value = true
}

// 导出处理
const handleExport = () => {
  exportSubTaskList(subTaskData.value, taskDetail.value?.taskName)
}

const onBatchDelete = () => {
  handleBatchDelete(selectedRows.value, refreshSubTasks)
}

const onBatchCopy = () => {
  handleBatchCopy(selectedRows.value, refreshSubTasks)
}

// 确认处理函数
const handleDifficultyAnalysisConfirm = (analysisData: any) => {
  console.log('难度分析结果:', analysisData)
  dialogStates.difficultyAnalysis = false
}

const handleReminderHistoryConfirm = (historyData: any) => {
  console.log('提醒历史结果:', historyData)
  dialogStates.reminderHistory = false
}

const handleReminderSettingConfirm = (reminderData: any) => {
  console.log('提醒设置结果:', reminderData)
  dialogStates.reminderSetting = false
  ElMessage.success(`子任务"${reminderData.taskName}"提醒设置成功`)
}

const handleRiskSettingConfirm = (riskData: any) => {
  console.log('风险设定结果:', riskData)
  dialogStates.riskSetting = false
  ElMessage.success(`子任务"${riskData.taskName}"风险设定成功`)
}

const handlePropertyManagementConfirm = (propertyData: any) => {
  console.log('属性管理结果:', propertyData)
  dialogStates.propertyManagement = false
  ElMessage.success(`子任务"${propertyData.taskName}"属性设置成功`)
}

// 处理表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const handleProgressConfigSave = (config: any) => {
  console.log('保存进度配置:', config)
  // 重新加载进度配置以确保实时更新
  loadProgressConfig()
  dialogStates.progressConfig = false
  ElMessage.success('进度配置保存成功')
}

const handlePageChange = (page: number) => {
  pagination.currentPage = page
}

// 子任务编辑确认处理
const onSubTaskEditConfirm = (data: any) => {
  handleSubTaskEditConfirm(data, taskId.value, refreshSubTasks)
  dialogStates.subTaskEdit = false
  currentEditData.subTask = null
}

// 新增子任务
const handleAddSubTask = () => {
  currentEditData.subTask = null
  dialogStates.subTaskEdit = true
}

// 紧急程度调整
const handleUrgencyAdjustment = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要调整的子任务')
    return
  }
  dialogStates.urgencyAdjustment = true
}

// 重要程度调整
const handleImportanceAdjustment = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要调整的子任务')
    return
  }
  dialogStates.importanceAdjustment = true
}

// 子任务资源
const handleSubTaskResource = () => {
  dialogStates.subTaskResource = true
}

// 子任务关系属性调整
const handleSubtaskAttributeManagement = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要调整的子任务')
    return
  }
  
  const currentQuery = route.query
  const taskId = route.params.id
  
  router.push({
    path: `/taskObjectiveDecomposition/subtaskAttributeManagement/${taskId}`,
    query: {
      ...currentQuery,
      selectedSubtasks: selectedRows.value.map(row => row.id).join(',')
    }
  })
}

// 紧急程度调整确认处理
const handleUrgencyAdjustmentConfirm = async (adjustmentData: any) => {
  try {
    console.log('紧急程度调整结果:', adjustmentData)

    let successCount = 0
    let failedTasks: string[] = []

    // 更新子任务的紧急程度
    adjustmentData.selectedTasks.forEach((task: any) => {
      try {
        const updated = taskStore.updateSubTask(task.id, {
          urgencyLevel: adjustmentData.newUrgencyLevel
        })
        if (updated) {
          successCount++
          console.log(`更新子任务 ${task.taskName} 的紧急程度为 ${adjustmentData.newUrgencyLevel}`)
        } else {
          failedTasks.push(task.taskName)
        }
      } catch (error) {
        console.error(`更新子任务 ${task.taskName} 失败:`, error)
        failedTasks.push(task.taskName)
      }
    })

    // 重新加载子任务数据
    refreshSubTasks()

    // 清空选中的行
    selectedRows.value = []

    dialogStates.urgencyAdjustment = false

    // 显示结果消息
    if (failedTasks.length === 0) {
      ElMessage.success(`成功调整 ${successCount} 个子任务的紧急程度`)
    } else if (successCount > 0) {
      ElMessage.warning(`成功调整 ${successCount} 个子任务，${failedTasks.length} 个失败`)
    } else {
      ElMessage.error('所有子任务调整失败，请重试')
    }
  } catch (error) {
    console.error('紧急程度调整处理失败:', error)
    ElMessage.error('紧急程度调整失败，请重试')
    dialogStates.urgencyAdjustment = false
  }
}

// 重要程度调整确认处理
const handleImportanceAdjustmentConfirm = async (adjustmentData: any) => {
  try {
    console.log('重要程度调整结果:', adjustmentData)

    let successCount = 0
    let failedTasks: string[] = []

    // 更新子任务的重要程度
    adjustmentData.selectedTasks.forEach((task: any) => {
      try {
        const updated = taskStore.updateSubTask(task.id, {
          importanceLevel: adjustmentData.newImportanceLevel
        })
        if (updated) {
          successCount++
          console.log(`更新子任务 ${task.taskName} 的重要程度为 ${adjustmentData.newImportanceLevel}`)
        } else {
          failedTasks.push(task.taskName)
        }
      } catch (error) {
        console.error(`更新子任务 ${task.taskName} 失败:`, error)
        failedTasks.push(task.taskName)
      }
    })

    // 重新加载子任务数据
    refreshSubTasks()

    // 清空选中的行
    selectedRows.value = []

    dialogStates.importanceAdjustment = false

    // 显示结果消息
    if (failedTasks.length === 0) {
      ElMessage.success(`成功调整 ${successCount} 个子任务的重要程度`)
    } else if (successCount > 0) {
      ElMessage.warning(`成功调整 ${successCount} 个子任务，${failedTasks.length} 个失败`)
    } else {
      ElMessage.error('所有子任务调整失败，请重试')
    }
  } catch (error) {
    console.error('重要程度调整处理失败:', error)
    ElMessage.error('重要程度调整失败，请重试')
    dialogStates.importanceAdjustment = false
  }
}

// 子任务资源确认处理
const handleSubTaskResourceConfirm = (resourceData: any) => {
  console.log('子任务资源配置结果:', resourceData)
  dialogStates.subTaskResource = false
  ElMessage.success('子任务资源配置成功')
}

// 子任务拆分确认处理
const handleSubTaskSplitConfirm = async (splitData: any) => {
  try {
    const success = await handleSubTaskSplitOperation(
      currentEditData.splitSubTask,
      splitData,
      refreshSubTasks
    )

    if (success) {
      dialogStates.subTaskSplit = false
      currentEditData.splitSubTask = null
    }
  } catch (error) {
    console.error('拆分处理失败:', error)
    ElMessage.error('拆分失败，请重试')
  }
}

// 搜索和重置
const handleSearch = () => {
  const count = searchSubTasks()
  ElMessage.success(`搜索完成，找到 ${count} 条记录`)
}

const handleReset = () => {
  resetSearch()
  ElMessage.info('已重置搜索条件')
}

// 还原演示数据
const handleResetDemoData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要还原所有演示数据吗？这将清除所有修改。',
      '还原演示数据',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 重置任务存储中的数据
    taskStore.resetDemoData()

    // 重新加载页面数据
    await loadTaskDetail()
    refreshSubTasks()

    // 重置搜索表单
    searchForm.taskName = ''
    searchForm.taskStatus = ''

    ElMessage.success('演示数据已还原')
  } catch {
    ElMessage.info('已取消还原')
  }
}

// 生命周期
onMounted(async () => {
  // 确保Store数据已初始化
  taskStore.initializeData()
  // 加载进度配置
  loadProgressConfig()
  await loadTaskDetail()
})

// 组件卸载时清理window对象上的函数
onUnmounted(() => {
  delete (window as any).canMoveUp
  delete (window as any).canMoveDown
})



// 确认批量状态更新
const confirmBatchStatusUpdate = () => {
  if (!selectedBatchStatus.value) {
    ElMessage.warning('请选择要更新的状态')
    return
  }

  let successCount = 0
  selectedRows.value.forEach(row => {
    const updated = taskStore.updateSubTask(row.id, { taskStatus: selectedBatchStatus.value as any })
    if (updated) successCount++
  })

  if (successCount > 0) {
    ElMessage.success(`成功更新 ${successCount} 个子任务状态`)
    // 重新加载子任务数据
    const subTasks = loadSubTasks()
    pagination.total = subTasks.length
    selectedRows.value = []
    batchStatusDialogVisible.value = false
  } else {
    ElMessage.error('状态更新失败')
  }
}

// 批量操作弹窗状态
const batchStatusDialogVisible = ref(false)
const selectedBatchStatus = ref('')
const batchAssignDialogVisible = ref(false)
const selectedResponsiblePerson = ref('')

// 风险分析相关状态
const riskAnalysisVisible = ref(false)

// 子任务历史相关状态
const subTaskHistoryVisible = ref(false)

// 子任务合并相关状态
const subTaskMergeVisible = ref(false)
const subTaskMergeLoading = ref(false)

// 合并表单数据
const mergeFormData = ref({
  taskType: '',
  taskName: '',
  taskCategory: '',
  taskDescription: '',
  priorityLevel: '',
  responsiblePerson: '',
  participants: []
})

// 任务类型选项
const taskTypeOptions = [
  { label: '业务报表子任务', value: 'business' },
  { label: '临时报表子任务', value: 'temporary' }
]

// 任务分类选项
const taskCategoryOptions = [
  { label: '党的建设', value: 'party_building' },
  { label: '经济发展', value: 'economic_development' },
  { label: '社会治理', value: 'social_governance' }
]



// 状态选项
const statusOptions = [
  { label: '未开始', value: 'not_started' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' },
  { label: '已取消', value: 'cancelled' }
]

// 责任人选项（模拟数据）
const responsiblePersonOptions = [
  { label: '张三 - 技术部 - 开发工程师', value: '张三' },
  { label: '李四 - 产品部 - 产品经理', value: '李四' },
  { label: '王五 - 设计部 - UI设计师', value: '王五' },
  { label: '赵六 - 测试部 - 测试工程师', value: '赵六' },
  { label: '钱七 - 运维部 - 运维工程师', value: '钱七' },
  { label: '孙八 - 技术部 - 高级开发工程师', value: '孙八' },
  { label: '周九 - 产品部 - 高级产品经理', value: '周九' },
  { label: '吴十 - 管理部 - 项目经理', value: '吴十' }
]

// 参与人选项（模拟数据）
const participantOptions = [
  { label: '张三 - 技术部 - 开发工程师', value: '张三' },
  { label: '李四 - 产品部 - 产品经理', value: '李四' },
  { label: '王五 - 设计部 - UI设计师', value: '王五' },
  { label: '赵六 - 测试部 - 测试工程师', value: '赵六' },
  { label: '钱七 - 运维部 - 运维工程师', value: '钱七' },
  { label: '孙八 - 技术部 - 高级开发工程师', value: '孙八' },
  { label: '周九 - 产品部 - 高级产品经理', value: '周九' },
  { label: '吴十 - 管理部 - 项目经理', value: '吴十' },
  { label: '刘一 - 财务部 - 财务专员', value: '刘一' },
  { label: '陈二 - 人事部 - 人事专员', value: '陈二' },
  { label: '杨三 - 市场部 - 市场专员', value: '杨三' },
  { label: '黄四 - 客服部 - 客服专员', value: '黄四' }
]

// 风险分析关闭处理
const handleRiskAnalysisClose = () => {
  riskAnalysisVisible.value = false
}



// 子任务合并处理函数
const handleSubTaskMergeClick = () => {
  if (selectedRows.value.length < 2) {
    ElMessage.warning('请至少选择两个子任务进行合并')
    return
  }

  subTaskMergeVisible.value = true
}

// 子任务关系审核处理函数
const handleSubTaskRelationAudit = () => {
  const currentQuery = route.query
  const taskId = route.params.id

  router.push({
    path: `/taskObjectiveDecomposition/relationAudit/${taskId}`,
    query: currentQuery
  })
}

// 确认子任务合并
const handleSubTaskMergeConfirm = async () => {
  if (!mergeFormData.value.taskType || !mergeFormData.value.taskName ||
      !mergeFormData.value.taskCategory || !mergeFormData.value.priorityLevel ||
      !mergeFormData.value.responsiblePerson || mergeFormData.value.participants.length === 0) {
    ElMessage.warning('请填写所有必填项')
    return
  }

  subTaskMergeLoading.value = true

  try {
    // 模拟合并处理
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟合并成功
    const success = true

    if (success) {
      subTaskMergeVisible.value = false
      ElMessage.success('子任务合并成功')
    }
  } catch (error) {
    ElMessage.error('合并失败，请重试')
  } finally {
    subTaskMergeLoading.value = false
  }
}

// 确认批量分配责任人
const confirmBatchAssign = () => {
  if (!selectedResponsiblePerson.value) {
    ElMessage.warning('请选择责任人')
    return
  }

  let successCount = 0
  selectedRows.value.forEach(row => {
    const updated = taskStore.updateSubTask(row.id, { responsiblePerson: selectedResponsiblePerson.value })
    if (updated) successCount++
  })

  if (successCount > 0) {
    ElMessage.success(`成功分配 ${successCount} 个子任务责任人`)
    // 重新加载子任务数据
    const subTasks = loadSubTasks()
    pagination.total = subTasks.length
    selectedRows.value = []
    batchAssignDialogVisible.value = false
  } else {
    ElMessage.error('责任人分配失败')
  }
}

// 生命周期
onMounted(async () => {
  // 确保Store数据已初始化
  taskStore.initializeData()
  // 加载进度配置
  loadProgressConfig()
  await loadTaskDetail()
})

// 组件卸载时清理window对象上的函数
onUnmounted(() => {
  delete (window as any).canMoveUp
  delete (window as any).canMoveDown
})
</script>

<template>
  <div class="task-detail-page" v-loading="loading">
    <!-- 页面头部导航 - 根据原型设计调整 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">任务目标拆解</h2>
      </div>
      <div class="header-actions">
        <!-- 隐蔽的演示数据还原按钮 -->
        <el-button
          @click="handleResetDemoData"
          class="reset-demo-button"
          size="small"
          type="text"
          title="还原演示数据"
        >
          🔄
        </el-button>
        <el-button @click="handleBack" class="return-button">返回</el-button>
      </div>
    </div>

    <!-- 任务基本信息卡片 - 根据原型设计调整布局 -->
    <div class="info-card" v-if="taskDetail">
      <div class="card-header">
        <h3>任务基本信息</h3>
      </div>
      <div class="card-content">
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">任务名称：</label>
            <span class="info-value">{{ taskDetail.taskName }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">任务类型：</label>
            <span class="info-value">{{ taskDetail.taskType }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">创建部门/创建责任人：</label>
            <span class="info-value">{{ taskDetail.createDepartment }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">开始时间：</label>
            <span class="info-value">{{ taskDetail.startTime }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">业务报表子任务数量：</label>
            <span class="info-value">{{ taskStatistics.businessReportCount }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">子任务提交进度：</label>
            <span class="info-value">{{ taskStatistics.submittedProgress }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">子任务退回统计：</label>
            <span class="info-value">{{ taskStatistics.rejectedCount }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">子任务通过统计：</label>
            <span class="info-value">{{ taskStatistics.approvedCount }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">驳回占比：</label>
            <span class="info-value">{{ taskStatistics.rejectionRatio }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 子任务列表卡片 -->
    <div class="subtask-card">
      <div class="card-header">
        <h3>详情</h3>
      </div>

      <!-- 筛选区域 - 根据原型设计调整 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">子任务列表：</label>
            <el-input
              v-model="searchForm.taskName"
              placeholder="请输入任务名称"
              class="filter-input"
              clearable
            />
            <el-select
              v-model="searchForm.taskStatus"
              placeholder="请选择任务状态"
              class="filter-select"
              clearable
            >
              <el-option label="未提交" value="未提交" />
              <el-option label="待审核" value="待审核" />
              <el-option label="已提交" value="已提交" />
              <el-option label="已退回" value="已退回" />
              <el-option label="已驳回" value="已驳回" />
              <el-option label="已完成" value="已完成" />
            </el-select>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="primary" @click="handleProgressConfig">进度配置</el-button>
            <el-button @click="handleExport">导出</el-button>
            <el-button @click="handleStyleTemplate">子任务关系展示</el-button>
            <el-button @click="handleRiskAnalysis">子任务风险分析</el-button>
            <el-button @click="handleSubTaskHistory">子任务历史</el-button>
            <el-button @click="handleSubTaskMergeClick" :disabled="selectedRows.length < 2">子任务合并</el-button>
            <el-button @click="handleSubTaskRelationAudit">子任务关系审核</el-button>
          </div>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <BaseTableComp
          :data="subTaskData"
          :colData="subTaskColumns"
          :buttons="subTaskButtons"
          :loading="loading"
          :checkbox="true"
          :visibleHeader="false"
          :visibleSetting="false"
          :currentPage="pagination.currentPage"
          :pageSize="pagination.pageSize"
          :total="pagination.total"
          :auto-height="true"
          style="height: 500px;"
          @clickButton="(data: any) => handleSubTaskButton(data.btn, data.scope)"
          @currentChange="handlePageChange"
          @selectionChange="handleSelectionChange"
        >
          <template #taskStatus="{ rowData }">
            <el-tag :type="getStatusType(rowData.taskStatus)">
              {{ rowData.taskStatus }}
            </el-tag>
          </template>

          <template #urgencyLevel="{ rowData }">
            <el-tag :type="getUrgencyType(rowData.urgencyLevel)">
              {{ getUrgencyLabel(rowData.urgencyLevel) }}
            </el-tag>
          </template>

          <template #progress="{ rowData }">
            <div class="progress-cell">
              <el-progress
                :percentage="rowData.progress || 0"
                :stroke-width="8"
                :show-text="false"
              />
              <span class="progress-text">{{ formatProgress(rowData.progress) }}</span>
            </div>
          </template>
        </BaseTableComp>
      </div>

      <!-- 批量操作 -->
      <div class="batch-operations">
        <el-button type="danger" @click="onBatchDelete" :disabled="selectedRows.length === 0">
          批量删除 ({{ selectedRows.length }})
        </el-button>
        <el-button type="primary" @click="batchDialogsRef?.openBatchStatusDialog()" :disabled="selectedRows.length === 0">
          批量状态更新
        </el-button>
        <el-button @click="batchDialogsRef?.openBatchAssignDialog()" :disabled="selectedRows.length === 0">
          批量分配责任人
        </el-button>
        <el-button type="success" @click="onBatchCopy" :disabled="selectedRows.length === 0">
          批量复制 ({{ selectedRows.length }})
        </el-button>
        <el-button type="info" @click="handleSubTaskResource">
          子任务资源
        </el-button>
        <el-button @click="handleAddSubTask">
          新增子任务
        </el-button>
        <el-button type="warning" @click="handleUrgencyAdjustment" :disabled="selectedRows.length === 0">
          紧急程度调整
        </el-button>
        <el-button type="primary" @click="handleImportanceAdjustment" :disabled="selectedRows.length === 0">
          重要程度调整
        </el-button>
        <el-button type="success" @click="handleSubtaskAttributeManagement" :disabled="selectedRows.length === 0">
          子任务关系属性调整
        </el-button>
      </div>
    </div>

    <!-- 进度配置弹窗 -->
    <ProgressConfigDialog
      v-model="dialogStates.progressConfig"
      @confirm="handleProgressConfigSave"
    />

    <!-- 难度分析弹窗 -->
    <DifficultyAnalysisDialog
      v-model="dialogStates.difficultyAnalysis"
      :task-data="taskDetail"
      @confirm="handleDifficultyAnalysisConfirm"
    />

    <!-- 时长分析弹窗 -->
    <DialogComp
      v-model="dialogStates.timeAnalysis"
      title="时长分析"
      width="600px"
      :visible-close-button="false"
      :visible-confirm-button="false"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <el-table :data="timeAnalysisData" style="width: 100%" height="auto">
          <el-table-column prop="stage" label="环节名称" min-width="150" />
          <el-table-column prop="duration" label="耗时" width="100" />
        </el-table>
        <div class="dialog-footer">
          <el-button @click="dialogStates.timeAnalysis = false">取消</el-button>
          <el-button type="primary" @click="dialogStates.timeAnalysis = false">确认</el-button>
        </div>
      </div>
    </DialogComp>

    <!-- 提醒历史弹窗 -->
    <ReminderHistoryDialog
      v-model="dialogStates.reminderHistory"
      :task-data="taskDetail"
      @confirm="handleReminderHistoryConfirm"
    />

    <!-- 子任务编辑弹窗 -->
    <SubTaskEditDialog
      v-model="dialogStates.subTaskEdit"
      :edit-data="currentEditData.subTask"
      @confirm="onSubTaskEditConfirm"
    />

    <!-- 子任务历史进度追踪弹窗 -->
    <DialogComp
      v-model="dialogStates.progressTracking"
      title="子任务历史进度追踪"
      width="800px"
      :visible-close-button="false"
      :visible-confirm-button="false"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <el-table :data="progressTrackingData" style="width: 100%">
          <el-table-column prop="milestone" label="进度节点" />
          <el-table-column prop="completionTime" label="完成时间" />
          <el-table-column prop="completionRate" label="达成进度" />
        </el-table>
        <div class="dialog-footer">
          <el-button @click="dialogStates.progressTracking = false">取消</el-button>
          <el-button type="primary" @click="dialogStates.progressTracking = false">确认</el-button>
        </div>
      </div>
    </DialogComp>

    <!-- 提醒设置弹窗 -->
    <ReminderSettingDialog
      v-model="dialogStates.reminderSetting"
      :task-data="currentEditData.reminderSubTask"
      @confirm="handleReminderSettingConfirm"
    />

    <!-- 风险设定弹窗 -->
    <RiskSettingDialog
      v-model="dialogStates.riskSetting"
      :task-data="currentEditData.riskSubTask"
      @confirm="handleRiskSettingConfirm"
    />

    <!-- 属性管理弹窗 -->
    <PropertyManagementDialog
      v-model="dialogStates.propertyManagement"
      :task-data="currentEditData.propertySubTask"
      @confirm="handlePropertyManagementConfirm"
    />

    <!-- 子任务关系展示弹窗 -->
    <SubtaskRelationDialog
      v-model:visible="dialogStates.styleTemplate"
      title="子任务关系展示"
      @close="dialogStates.styleTemplate = false"
    />

    <!-- 风险分析弹窗 - 暂时使用简单弹窗替代 -->
    <el-dialog
      v-model="riskAnalysisVisible"
      title="风险分析"
      width="800px"
    >
      <div>风险分析功能正在开发中...</div>
      <template #footer>
        <el-button @click="riskAnalysisVisible = false">关闭</el-button>
      </template>
    </el-dialog>



    <!-- 批量状态更新弹窗 -->
    <el-dialog
      v-model="batchStatusDialogVisible"
      title="批量状态更新"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="batch-dialog-content">
        <p class="batch-instruction">请选择要更新的状态：</p>
        <el-select
          v-model="selectedBatchStatus"
          placeholder="请选择状态"
          style="width: 100%"
        >
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <p class="selected-count">已选择 {{ selectedRows.length }} 个子任务</p>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchStatusDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchStatusUpdate">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量分配责任人弹窗 -->
    <el-dialog
      v-model="batchAssignDialogVisible"
      title="批量分配责任人"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="batch-dialog-content">
        <p class="batch-instruction">请选择责任人：</p>
        <el-select
          v-model="selectedResponsiblePerson"
          placeholder="请选择责任人"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="option in responsiblePersonOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <p class="selected-count">已选择 {{ selectedRows.length }} 个子任务</p>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchAssignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchAssign">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 风险分析弹窗 -->
    <RiskAnalysisDialog
      v-model:visible="riskAnalysisVisible"
      :task-id="Array.isArray(route.params.id) ? route.params.id[0] : route.params.id"
      @close="handleRiskAnalysisClose"
    />

    <!-- 子任务历史弹窗 -->
    <SubTaskHistoryDialog
      v-model:visible="subTaskHistoryVisible"
      :task-id="Array.isArray(route.params.id) ? route.params.id[0] : route.params.id"
      @close="() => subTaskHistoryVisible = false"
    />





    <!-- 子任务合并弹窗 -->
    <el-dialog
      v-model="subTaskMergeVisible"
      title="报表子任务合并"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="subtask-merge-content" v-loading="subTaskMergeLoading" element-loading-text="合并处理中...">
        <el-form :model="mergeFormData" label-width="140px" v-if="!subTaskMergeLoading">
          <el-form-item label="子任务类型" required>
            <span class="required-mark">*</span>
            <el-select
              v-model="mergeFormData.taskType"
              placeholder="下拉单选：业务报表子任务、临时报表子任务"
              style="width: 100%"
            >
              <el-option
                v-for="option in taskTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="合并后子任务名称" required>
            <span class="required-mark">*</span>
            <el-input
              v-model="mergeFormData.taskName"
              placeholder="请输入当前子任务名称"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="子任务分类" required>
            <span class="required-mark">*</span>
            <el-select
              v-model="mergeFormData.taskCategory"
              placeholder="下拉单选：党的建设"
              style="width: 100%"
            >
              <el-option
                v-for="option in taskCategoryOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="责任人" required>
            <span class="required-mark">*</span>
            <el-select
              v-model="mergeFormData.responsiblePerson"
              placeholder="请选择人员（范围为本部门）"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="option in responsiblePersonOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="参与人" required>
            <span class="required-mark">*</span>
            <el-select
              v-model="mergeFormData.participants"
              placeholder="请选择人员（范围为本部门）"
              style="width: 100%"
              multiple
              filterable
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="option in participantOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <!-- 选中任务提示 -->
        <div class="selected-tasks-info" v-if="selectedRows.length > 0">
          <p class="info-text">已选择 {{ selectedRows.length }} 个子任务进行合并：</p>
          <ul class="task-list">
            <li v-for="task in selectedRows" :key="task.id" class="task-item">
              {{ task.taskName }}
            </li>
          </ul>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="subTaskMergeVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubTaskMergeConfirm" :loading="subTaskMergeLoading">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 紧急程度调整弹窗 -->
    <UrgencyAdjustmentDialog
      v-model="dialogStates.urgencyAdjustment"
      :sub-task-data="subTaskData"
      @confirm="handleUrgencyAdjustmentConfirm"
    />

    <!-- 重要程度调整弹窗 -->
    <ImportanceAdjustmentDialog
      v-model="dialogStates.importanceAdjustment"
      :sub-task-data="subTaskData"
      @confirm="handleImportanceAdjustmentConfirm"
    />

    <!-- 子任务资源弹窗 -->
    <SubTaskResourceDialog
      v-model="dialogStates.subTaskResource"
      @confirm="handleSubTaskResourceConfirm"
    />

    <!-- 子任务拆分弹窗 -->
    <SubTaskSplitDialog
      v-model="dialogStates.subTaskSplit"
      :task-data="currentEditData.splitSubTask"
      @confirm="handleSubTaskSplitConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
// 隐蔽的演示数据还原按钮样式
.reset-demo-button {
  opacity: 0.3;
  font-size: 12px;
  margin-right: 10px;
  transition: opacity 0.3s ease;
  color: #909399;

  &:hover {
    opacity: 1;
    color: #409eff;
  }
}

.task-detail-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部导航 - 根据原型设计调整
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-left {
      .page-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .header-actions {
      .return-button {
        padding: 8px 16px;
        font-size: 14px;
      }
    }
  }

  // 信息卡片样式
  .info-card, .subtask-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background: #fafbfc;
      border-bottom: 1px solid #e4e7ed;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }

    .card-content {
      padding: 24px;
    }
  }

  // 信息网格布局
  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px 40px;

    .info-item {
      display: flex;
      align-items: center;

      .info-label {
        width: 140px;
        text-align: right;
        font-weight: 500;
        color: #606266;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .info-value {
        color: #303133;
        flex: 1;
      }
    }
  }

  // 筛选区域 - 根据原型设计调整
  .filter-section {
    padding: 20px 24px;
    background: #fafbfc;
    border-bottom: 1px solid #e4e7ed;

    .filter-row {
      .filter-group {
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;

        .filter-label {
          width: 140px;
          text-align: right;
          font-weight: 500;
          color: #606266;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .filter-input {
          width: 200px;
        }

        .filter-select {
          width: 150px;
        }
      }
    }
  }

  // 表格区域
  .table-section {
    padding: 24px;

    .progress-cell {
      display: flex;
      align-items: center;
      gap: 10px;

      .el-progress {
        flex: 1;
      }

      .progress-text {
        font-size: 12px;
        color: #666;
        min-width: 35px;
      }
    }
  }

  // 批量操作
  .batch-operations {
    padding: 16px 24px;
    background: #fafbfc;
    border-top: 1px solid #e4e7ed;
  }

  // 弹窗内容样式
  .dialog-content {
    padding: 20px;
  }

  // 进度配置样式已移至ProgressConfigDialog组件中

  // 难度分析样式已移至DifficultyAnalysisDialog组件中

  // 通用弹窗底部样式
  .dialog-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 更多操作弹窗样式
:deep(.more-actions-dialog) {
  .el-message-box__content {
    padding: 20px;
  }

  .more-actions-menu {
    .action-group {
      .el-button {
        padding: 8px 12px;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

// 批量操作弹窗样式
.batch-dialog-content {
  .batch-instruction {
    margin-bottom: 16px;
    font-size: 14px;
    color: #303133;
  }

  .selected-count {
    margin-top: 12px;
    margin-bottom: 0;
    font-size: 12px;
    color: #909399;
  }
}

// 风险分析弹窗样式
.risk-analysis-content {
  .risk-analysis-header {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
  }

  .risk-analysis-table {
    min-height: 200px;
  }
}

// 风险处理弹窗样式
.risk-processing-content {
  .risk-processing-table {
    margin-bottom: 24px;
  }

  .subtask-processing {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .processing-title {
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #303133;

      .required-mark {
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .processing-options {
      display: flex;
      align-items: center;
      gap: 12px;

      .options-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .knowledge-search {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .search-title {
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    .search-input-group {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }

    .search-result {
      padding: 12px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
    }
  }
}

// 子任务历史记录弹窗样式
.subtask-history-content {
  min-height: 200px;
}

// 子任务合并弹窗样式
.subtask-merge-content {
  .required-mark {
    color: #f56c6c;
    margin-right: 4px;
  }

  .selected-tasks-info {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;

    .info-text {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    .task-list {
      margin: 0;
      padding: 0;
      list-style: none;

      .task-item {
        padding: 4px 0;
        font-size: 13px;
        color: #606266;
        position: relative;
        padding-left: 16px;

        &:before {
          content: '•';
          position: absolute;
          left: 0;
          color: #409eff;
        }
      }
    }
  }
}
</style>
