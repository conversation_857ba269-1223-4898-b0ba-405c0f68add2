<!-- 质量监控与评估弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据集质量评估"
    width="800px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="取消"
    @click-cancel="handleClose"
  >
    <div class="quality-monitoring-assessment-content" style="padding: 20px; min-height: 500px;">
      <!-- 顶部按钮 -->
      <div style="margin-bottom: 20px; display: flex; gap: 10px;">
        <el-button size="small" type="primary" @click="onClickAddAssessment">新增</el-button>
        <el-button size="small" type="primary" @click="onClickQualityMonitoring">质量监控</el-button>
      </div>

      <!-- 质量评估列表 -->
      <el-table :data="assessmentList" border style="width: 100%">
        <el-table-column prop="evaluationDate" label="评估时间" width="120" align="center" />
        <el-table-column prop="completeness" label="完整性评分" width="100" align="center" />
        <el-table-column prop="consistency" label="一致性评分" width="100" align="center" />
        <el-table-column prop="accuracy" label="准确性评分" width="100" align="center" />
        <el-table-column prop="uniqueness" label="唯一性评分" width="100" align="center" />
        <el-table-column prop="timeliness" label="时效性评分" width="100" align="center" />
        <el-table-column prop="totalScore" label="综合评分" width="100" align="center" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" link @click="onClickEditAssessment(row)">修改</el-button>
            <el-button size="small" type="danger" link @click="onClickDeleteAssessment(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑质量评估弹窗 -->
    <Dialog
      v-model="showAddAssessmentDialog"
      title="数据集质量评估"
      width="400px"
      :destroy-on-close="true"
      @click-confirm="onConfirmAddAssessment"
      @click-cancel="handleCloseAddAssessment"
    >
      <div style="padding: 20px;">
        <div style="margin-bottom: 10px; color: #666; font-size: 12px;">
          提示：评估时间为必填项，五个评分的总和不能超过100
        </div>
        
        <el-form :model="assessmentForm" label-width="80px">
          <el-form-item label="评估时间:" required>
            <el-input v-model="assessmentForm.evaluationDate" placeholder="请输入日期" />
          </el-form-item>
          
          <el-form-item label="完整性:">
            <el-input 
              v-model.number="assessmentForm.completeness" 
              placeholder="请输入分数"
              type="number"
              :min="0"
              :max="100"
            />
          </el-form-item>
          
          <el-form-item label="一致性:">
            <el-input 
              v-model.number="assessmentForm.consistency" 
              placeholder="请输入分数"
              type="number"
              :min="0"
              :max="100"
            />
          </el-form-item>
          
          <el-form-item label="准确性:">
            <el-input 
              v-model.number="assessmentForm.accuracy" 
              placeholder="请输入分数"
              type="number"
              :min="0"
              :max="100"
            />
          </el-form-item>
          
          <el-form-item label="唯一性:">
            <el-input 
              v-model.number="assessmentForm.uniqueness" 
              placeholder="请输入分数"
              type="number"
              :min="0"
              :max="100"
            />
          </el-form-item>
          
          <el-form-item label="时效性:">
            <el-input 
              v-model.number="assessmentForm.timeliness" 
              placeholder="请输入分数"
              type="number"
              :min="0"
              :max="100"
            />
          </el-form-item>
        </el-form>
      </div>
    </Dialog>

    <!-- 质量监控弹窗 -->
    <Dialog
      v-model="showQualityMonitoringDialog"
      title="数据集质量监控"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="确定"
      @click-cancel="showQualityMonitoringDialog = false"
    >
      <div class="quality-monitoring-content" style="padding: 20px;">
        <!-- 数据集质量监控 -->
        <div class="monitoring-header">
          <h4 style="margin-bottom: 20px; color: #333;">数据集质量监控</h4>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-cards" style="display: flex; gap: 20px; margin-bottom: 30px;">
          <div class="stat-card" style="flex: 1; background: #f0f9ff; padding: 20px; border-radius: 8px; text-align: center;">
            <div class="stat-icon" style="width: 40px; height: 40px; background: #3b82f6; border-radius: 50%; margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: bold;">{{ totalDataSets }}</div>
            <div class="stat-label" style="color: #666; font-size: 14px;">总数据集数量</div>
          </div>

          <div class="stat-card" style="flex: 1; background: #f0fdf4; padding: 20px; border-radius: 8px; text-align: center;">
            <div class="stat-icon" style="width: 40px; height: 40px; background: #22c55e; border-radius: 50%; margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: bold;">{{ todayNewDataSets }}</div>
            <div class="stat-label" style="color: #666; font-size: 14px;">今日新增数据集</div>
          </div>

          <div class="stat-card" style="flex: 1; background: #fefce8; padding: 20px; border-radius: 8px; text-align: center;">
            <div class="stat-icon" style="width: 40px; height: 40px; background: #eab308; border-radius: 50%; margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: bold;">{{ yesterdayNewDataSets }}</div>
            <div class="stat-label" style="color: #666; font-size: 14px;">昨日新增数据集</div>
          </div>
        </div>

        <!-- 近期质量趋势 -->
        <div class="quality-trend">
          <h5 style="margin-bottom: 15px; color: #333;">近期质量趋势</h5>
          <div ref="chartContainer" style="width: 100%; height: 400px;"></div>
        </div>
      </div>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'

// 质量评估接口
interface QualityAssessment {
  id: string
  evaluationDate: string
  completeness: number
  consistency: number
  accuracy: number
  uniqueness: number
  timeliness: number
  totalScore: number
  createTime: string
}

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showAddAssessmentDialog = ref(false)
const showQualityMonitoringDialog = ref(false)
const currentEditAssessment = ref<QualityAssessment | null>(null)
const chartContainer = ref<HTMLDivElement>()
const chartInstance = ref<echarts.ECharts | null>(null)

// 质量评估列表
const assessmentList = ref<QualityAssessment[]>([])

// 表单数据
const assessmentForm = ref({
  evaluationDate: '',
  completeness: 0,
  consistency: 0,
  accuracy: 0,
  uniqueness: 0,
  timeliness: 0
})

// 缓存键
const STORAGE_KEY = 'qualityAssessmentData'
const DATASET_STORAGE_KEY = 'dataSetManagement_data'

// 统计数据计算
const totalDataSets = computed(() => {
  try {
    const cached = localStorage.getItem(DATASET_STORAGE_KEY)
    if (cached) {
      const dataSetList = JSON.parse(cached)
      return dataSetList.length
    }
  } catch (error) {
    console.error('获取数据集总数失败:', error)
  }
  return 0
})

const todayNewDataSets = computed(() => {
  try {
    const cached = localStorage.getItem(DATASET_STORAGE_KEY)
    if (cached) {
      const dataSetList = JSON.parse(cached)
      const today = new Date().toDateString()
      return dataSetList.filter((item: any) => {
        const createDate = new Date(item.createTime).toDateString()
        return createDate === today
      }).length
    }
  } catch (error) {
    console.error('获取今日新增数据集失败:', error)
  }
  return 0
})

const yesterdayNewDataSets = computed(() => {
  try {
    const cached = localStorage.getItem(DATASET_STORAGE_KEY)
    if (cached) {
      const dataSetList = JSON.parse(cached)
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = yesterday.toDateString()
      return dataSetList.filter((item: any) => {
        const createDate = new Date(item.createTime).toDateString()
        return createDate === yesterdayStr
      }).length
    }
  } catch (error) {
    console.error('获取昨日新增数据集失败:', error)
  }
  return 0
})

// 初始化模拟数据
const initMockData = () => {
  const mockData: QualityAssessment[] = [
    {
      id: '1',
      evaluationDate: '2025.7.14',
      completeness: 20,
      consistency: 20,
      accuracy: 20,
      uniqueness: 10,
      timeliness: 20,
      totalScore: 90,
      createTime: '2024-01-15 10:30:00'
    },
    {
      id: '2',
      evaluationDate: '2025.7.15',
      completeness: 20,
      consistency: 20,
      accuracy: 20,
      uniqueness: 10,
      timeliness: 20,
      totalScore: 90,
      createTime: '2024-01-14 09:15:00'
    },
    {
      id: '3',
      evaluationDate: '2025.7.16',
      completeness: 20,
      consistency: 20,
      accuracy: 20,
      uniqueness: 10,
      timeliness: 20,
      totalScore: 90,
      createTime: '2024-01-13 15:20:00'
    },
    {
      id: '4',
      evaluationDate: '2025.7.17',
      completeness: 20,
      consistency: 20,
      accuracy: 20,
      uniqueness: 10,
      timeliness: 20,
      totalScore: 90,
      createTime: '2024-01-12 11:45:00'
    },
    {
      id: '5',
      evaluationDate: '2025.7.18',
      completeness: 20,
      consistency: 20,
      accuracy: 20,
      uniqueness: 10,
      timeliness: 20,
      totalScore: 90,
      createTime: '2024-01-11 14:20:00'
    },
    {
      id: '6',
      evaluationDate: '2025.7.19',
      completeness: 20,
      consistency: 20,
      accuracy: 20,
      uniqueness: 10,
      timeliness: 20,
      totalScore: 90,
      createTime: '2024-01-10 16:30:00'
    },
    {
      id: '7',
      evaluationDate: '2025.7.14',
      completeness: 20,
      consistency: 20,
      accuracy: 20,
      uniqueness: 10,
      timeliness: 20,
      totalScore: 90,
      createTime: '2024-01-09 09:45:00'
    }
  ]
  
  assessmentList.value = mockData
  saveDataToCache()
}

// 加载数据
const loadDataFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      assessmentList.value = JSON.parse(cached)
    } else {
      initMockData()
    }
  } catch (error) {
    console.error('加载质量评估数据失败:', error)
    initMockData()
  }
}

// 保存数据
const saveDataToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(assessmentList.value))
  } catch (error) {
    console.error('保存质量评估数据失败:', error)
  }
}

// 重置表单
const resetAssessmentForm = () => {
  assessmentForm.value = {
    evaluationDate: '',
    completeness: 0,
    consistency: 0,
    accuracy: 0,
    uniqueness: 0,
    timeliness: 0
  }
  currentEditAssessment.value = null
}

// 验证评分总和
const validateScoreSum = () => {
  const total = assessmentForm.value.completeness + 
                assessmentForm.value.consistency + 
                assessmentForm.value.accuracy + 
                assessmentForm.value.uniqueness + 
                assessmentForm.value.timeliness
  return total <= 100
}

// 关闭主弹窗
const handleClose = () => {
  visible.value = false
}

// 新增质量评估
const onClickAddAssessment = () => {
  resetAssessmentForm()
  showAddAssessmentDialog.value = true
}

// 编辑质量评估
const onClickEditAssessment = (row: QualityAssessment) => {
  currentEditAssessment.value = row
  assessmentForm.value = {
    evaluationDate: row.evaluationDate,
    completeness: row.completeness,
    consistency: row.consistency,
    accuracy: row.accuracy,
    uniqueness: row.uniqueness,
    timeliness: row.timeliness
  }
  showAddAssessmentDialog.value = true
}

// 删除质量评估
const onClickDeleteAssessment = (row: QualityAssessment) => {
  ElMessageBox.confirm(
    `确定要删除评估时间为"${row.evaluationDate}"的质量评估记录吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = assessmentList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      assessmentList.value.splice(index, 1)
      saveDataToCache()
      ElMessage.success('删除成功')
    }
  })
}

// 质量监控
const onClickQualityMonitoring = () => {
  showQualityMonitoringDialog.value = true
  nextTick(() => {
    initChart()
  })
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return

  if (chartInstance.value) {
    chartInstance.value.dispose()
  }

  chartInstance.value = echarts.init(chartContainer.value)

  const option = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['完整性', '一致性', '准确性', '唯一性', '时效性'],
      top: 20
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: assessmentList.value.map(item => item.evaluationDate)
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100
    },
    series: [
      {
        name: '完整性',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: assessmentList.value.map(item => item.completeness)
      },
      {
        name: '一致性',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: assessmentList.value.map(item => item.consistency)
      },
      {
        name: '准确性',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: assessmentList.value.map(item => item.accuracy)
      },
      {
        name: '唯一性',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: assessmentList.value.map(item => item.uniqueness)
      },
      {
        name: '时效性',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: assessmentList.value.map(item => item.timeliness)
      }
    ]
  }

  chartInstance.value.setOption(option)
}

// 关闭新增/编辑弹窗
const handleCloseAddAssessment = () => {
  showAddAssessmentDialog.value = false
  resetAssessmentForm()
}

// 确认新增/编辑
const onConfirmAddAssessment = () => {
  if (!assessmentForm.value.evaluationDate.trim()) {
    ElMessage.error('请输入评估时间')
    return
  }

  if (!validateScoreSum()) {
    ElMessage.error('五个评分的总和不能超过100')
    return
  }

  const totalScore = assessmentForm.value.completeness + 
                    assessmentForm.value.consistency + 
                    assessmentForm.value.accuracy + 
                    assessmentForm.value.uniqueness + 
                    assessmentForm.value.timeliness

  if (currentEditAssessment.value) {
    // 编辑模式
    const index = assessmentList.value.findIndex(item => item.id === currentEditAssessment.value!.id)
    if (index !== -1) {
      assessmentList.value[index] = {
        ...assessmentList.value[index],
        evaluationDate: assessmentForm.value.evaluationDate,
        completeness: assessmentForm.value.completeness,
        consistency: assessmentForm.value.consistency,
        accuracy: assessmentForm.value.accuracy,
        uniqueness: assessmentForm.value.uniqueness,
        timeliness: assessmentForm.value.timeliness,
        totalScore: totalScore
      }
      ElMessage.success('修改成功')
    }
  } else {
    // 新增模式
    const newAssessment: QualityAssessment = {
      id: Date.now().toString(),
      evaluationDate: assessmentForm.value.evaluationDate,
      completeness: assessmentForm.value.completeness,
      consistency: assessmentForm.value.consistency,
      accuracy: assessmentForm.value.accuracy,
      uniqueness: assessmentForm.value.uniqueness,
      timeliness: assessmentForm.value.timeliness,
      totalScore: totalScore,
      createTime: new Date().toLocaleString()
    }
    assessmentList.value.unshift(newAssessment)
    ElMessage.success('新增成功')
  }

  saveDataToCache()
  showAddAssessmentDialog.value = false
  resetAssessmentForm()
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadDataFromCache()
  }
})
</script>

<style scoped>
.quality-monitoring-assessment-content {
  display: flex;
  flex-direction: column;
}
</style>
