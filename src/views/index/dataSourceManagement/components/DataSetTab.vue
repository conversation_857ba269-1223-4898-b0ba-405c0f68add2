<!-- 数据集Tab组件 -->
<template>
  <div class="data-set-tab">
    <Block title="数据集管理" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <div class="top-buttons">
          <el-button size="small" type="primary" @click="onClickAdd">新增</el-button>
          <el-button size="small" type="primary" @click="onClickBatchDelete">批量删除</el-button>
          <el-button size="small" type="primary" @click="onClickBatchExport">批量导出</el-button>
          <el-button size="small" type="primary" @click="onClickBatchImport">批量导入</el-button>
          <el-button size="small" type="primary" @click="onClickUpdateLog">更新日志</el-button>
          <el-button size="small" type="primary" @click="onClickFieldTemplate">字段模板</el-button>
          
          <el-dropdown @command="handleMoreCommand">
            <el-button size="small" type="primary">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="operationButtonConfig">操作按钮隐藏配置</el-dropdown-item>
                <el-dropdown-item command="dataSetTypeConfig">数据集类型配置</el-dropdown-item>
                <el-dropdown-item command="dataSetCategory">数据集分类</el-dropdown-item>
                <el-dropdown-item command="qualityMonitoringAssessment">质量监控与评估</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>

      <!-- 搜索表单 -->
      <Form
        ref="searchFormRef"
        v-model="searchForm"
        :props="searchFormProp"
        :enable-button="true"
        @click-search="onSearch"
        @click-reset="onReset"
      />

      <!-- 数据表格 -->
      <BaseTableComp
        :data="paginatedData"
        :columns="tableColumns"
        :buttons="tableButtons"
        :pagination="pagination"
        :height="tableHeight"
        @page-change="onPageChange"
        @size-change="onSizeChange"
        @selection-change="onSelectionChange"
        @button-click="onTableButtonClick"
      />
    </Block>

    <!-- 新增/编辑/详情弹窗 -->
    <Dialog
      width="35%"
      v-model="showDialogForm"
      :title="dialogMode === 'add' ? '新增数据集' : dialogMode === 'edit' ? '编辑数据集' : '数据集详情'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="currentRow = null; dialogForm = {}"
      @click-confirm="onDialogConfirm"
    >
      <!-- 基础信息表单 -->
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
        :disabled="dialogMode === 'view'"
      />
    </Dialog>

    <!-- 操作按钮隐藏配置弹窗 -->
    <OperationButtonConfigDialog
      v-model="showOperationButtonConfigDialog"
    />

    <!-- 数据集类型配置弹窗 -->
    <DataSetTypeConfigDialog
      v-model="showDataSetTypeConfigDialog"
    />

    <!-- 数据集分类弹窗 -->
    <DataSetCategoryDialog
      v-model="showDataSetCategoryDialog"
    />

    <!-- 质量监控与评估弹窗 -->
    <QualityMonitoringAssessmentDialog
      v-model="showQualityMonitoringAssessmentDialog"
    />
  </div>
</template>

<script setup lang="ts" name="DataSetTab">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

// 导入弹窗组件（稍后创建）
import OperationButtonConfigDialog from './OperationButtonConfigDialog.vue'
import DataSetTypeConfigDialog from './DataSetTypeConfigDialog.vue'
import DataSetCategoryDialog from './DataSetCategoryDialog.vue'
import QualityMonitoringAssessmentDialog from './QualityMonitoringAssessmentDialog.vue'

// 数据集类型定义
interface DataSet {
  id: string
  name: string
  description: string
  type: string
  category: string
  status: boolean
  createTime: string
  createUser: string
  dataSize: string
  recordCount: number
  lastUpdateTime: string
}

// 响应式数据
const loading = ref(false)
const tableHeight = ref(400)
const currentRow = ref<DataSet | null>(null)
const selectedRows = ref<DataSet[]>([])

// 搜索表单
const searchFormProp = ref([
  { label: '数据集名称', prop: 'name', type: 'text' },
  { label: '数据集类型', prop: 'type', type: 'select', options: [
    { label: '结构化数据', value: 'structured' },
    { label: '半结构化数据', value: 'semi-structured' },
    { label: '非结构化数据', value: 'unstructured' }
  ]},
  { label: '数据集分类', prop: 'category', type: 'select', options: [
    { label: '业务数据', value: 'business' },
    { label: '日志数据', value: 'log' },
    { label: '监控数据', value: 'monitor' }
  ]},
  { label: '状态', prop: 'status', type: 'select', options: [
    { label: '启用', value: true },
    { label: '禁用', value: false }
  ]}
])

const searchForm = ref({
  name: '',
  type: '',
  category: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格列配置
const tableColumns = ref([
  { prop: 'name', label: '数据集名称', width: 150 },
  { prop: 'description', label: '描述', width: 200 },
  { prop: 'type', label: '类型', width: 120 },
  { prop: 'category', label: '分类', width: 120 },
  { prop: 'dataSize', label: '数据大小', width: 100 },
  { prop: 'recordCount', label: '记录数', width: 100 },
  { prop: 'status', label: '状态', width: 80, type: 'switch' },
  { prop: 'createTime', label: '创建时间', width: 160 },
  { prop: 'createUser', label: '创建人', width: 100 },
  { prop: 'lastUpdateTime', label: '最后更新时间', width: 160 }
])

// 表格操作按钮
const tableButtons = ref([
  { label: '查看', type: 'primary', size: 'small', command: 'view' },
  { label: '编辑', type: 'primary', size: 'small', command: 'edit' },
  { label: '删除', type: 'danger', size: 'small', command: 'delete' }
])

// 弹窗相关
const showDialogForm = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const dialogForm = ref<Partial<DataSet>>({})

// 弹窗表单配置
const dialogFormProps = computed(() => [
  { label: '数据集名称', prop: 'name', type: 'text', required: true },
  { label: '描述', prop: 'description', type: 'textarea', required: true },
  { label: '数据集类型', prop: 'type', type: 'select', required: true, options: [
    { label: '结构化数据', value: 'structured' },
    { label: '半结构化数据', value: 'semi-structured' },
    { label: '非结构化数据', value: 'unstructured' }
  ]},
  { label: '数据集分类', prop: 'category', type: 'select', required: true, options: [
    { label: '业务数据', value: 'business' },
    { label: '日志数据', value: 'log' },
    { label: '监控数据', value: 'monitor' }
  ]},
  { label: '状态', prop: 'status', type: 'switch', required: true }
])

// 表单验证规则
const dialogFormRules = {
  name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据集类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择数据集分类', trigger: 'change' }]
}

// 各种弹窗显示状态
const showOperationButtonConfigDialog = ref(false)
const showDataSetTypeConfigDialog = ref(false)
const showDataSetCategoryDialog = ref(false)
const showQualityMonitoringAssessmentDialog = ref(false)

// 缓存键
const STORAGE_KEY = 'dataSetManagement_data'

// 模拟数据
const dataSetList = ref<DataSet[]>([])

// 初始化模拟数据
const initMockData = () => {
  const mockData: DataSet[] = [
    {
      id: '1',
      name: '用户行为数据集',
      description: '用户在平台上的行为数据，包括点击、浏览、购买等',
      type: 'structured',
      category: 'business',
      status: true,
      createTime: '2024-01-15 10:30:00',
      createUser: '张三',
      dataSize: '2.5GB',
      recordCount: 1250000,
      lastUpdateTime: '2024-01-20 14:20:00'
    },
    {
      id: '2',
      name: '系统日志数据集',
      description: '系统运行过程中产生的日志信息',
      type: 'semi-structured',
      category: 'log',
      status: true,
      createTime: '2024-01-14 09:15:00',
      createUser: '李四',
      dataSize: '5.2GB',
      recordCount: 3200000,
      lastUpdateTime: '2024-01-21 16:45:00'
    },
    {
      id: '3',
      name: '监控指标数据集',
      description: '系统性能监控指标数据',
      type: 'structured',
      category: 'monitor',
      status: false,
      createTime: '2024-01-13 15:20:00',
      createUser: '王五',
      dataSize: '1.8GB',
      recordCount: 890000,
      lastUpdateTime: '2024-01-19 11:30:00'
    }
  ]
  
  dataSetList.value = mockData
  saveDataToCache()
}

// 缓存操作
const loadDataFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      dataSetList.value = JSON.parse(cached)
    } else {
      initMockData()
    }
  } catch (error) {
    console.error('加载数据集数据失败:', error)
    initMockData()
  }
}

const saveDataToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataSetList.value))
  } catch (error) {
    console.error('保存数据集数据失败:', error)
  }
}

// 计算属性
const filteredData = computed(() => {
  let filtered = dataSetList.value
  
  if (searchForm.value.name) {
    filtered = filtered.filter(item => 
      item.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }
  
  if (searchForm.value.type) {
    filtered = filtered.filter(item => item.type === searchForm.value.type)
  }
  
  if (searchForm.value.category) {
    filtered = filtered.filter(item => item.category === searchForm.value.category)
  }
  
  if (searchForm.value.status !== '') {
    filtered = filtered.filter(item => item.status === searchForm.value.status)
  }
  
  pagination.total = filtered.length
  return filtered
})

const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 事件处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 200
}

const onSearch = () => {
  pagination.page = 1
}

const onReset = () => {
  searchForm.value = {
    name: '',
    type: '',
    category: '',
    status: ''
  }
  pagination.page = 1
}

const onPageChange = (page: number) => {
  pagination.page = page
}

const onSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

const onSelectionChange = (selection: DataSet[]) => {
  selectedRows.value = selection
}

// 顶部按钮事件
const onClickAdd = () => {
  dialogMode.value = 'add'
  dialogForm.value = {
    status: true
  }
  showDialogForm.value = true
}

const onClickBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条数据吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const selectedIds = selectedRows.value.map(row => row.id)
    dataSetList.value = dataSetList.value.filter(item => !selectedIds.includes(item.id))
    saveDataToCache()
    ElMessage.success('删除成功')
  })
}

const onClickBatchExport = () => {
  ElMessage.info('批量导出功能开发中...')
}

const onClickBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

const onClickUpdateLog = () => {
  ElMessage.info('更新日志功能开发中...')
}

const onClickFieldTemplate = () => {
  ElMessage.info('字段模板功能开发中...')
}

// 更多操作下拉菜单
const handleMoreCommand = (command: string) => {
  switch (command) {
    case 'operationButtonConfig':
      showOperationButtonConfigDialog.value = true
      break
    case 'dataSetTypeConfig':
      showDataSetTypeConfigDialog.value = true
      break
    case 'dataSetCategory':
      showDataSetCategoryDialog.value = true
      break
    case 'qualityMonitoringAssessment':
      showQualityMonitoringAssessmentDialog.value = true
      break
  }
}

// 表格按钮点击事件
const onTableButtonClick = (command: string, row: DataSet) => {
  currentRow.value = row

  switch (command) {
    case 'view':
      dialogMode.value = 'view'
      dialogForm.value = { ...row }
      showDialogForm.value = true
      break
    case 'edit':
      dialogMode.value = 'edit'
      dialogForm.value = { ...row }
      showDialogForm.value = true
      break
    case 'delete':
      ElMessageBox.confirm(
        `确定要删除数据集"${row.name}"吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        const index = dataSetList.value.findIndex(item => item.id === row.id)
        if (index !== -1) {
          dataSetList.value.splice(index, 1)
          saveDataToCache()
          ElMessage.success('删除成功')
        }
      })
      break
  }
}

// 弹窗确认
const onDialogConfirm = () => {
  // 表单验证逻辑
  loading.value = true

  setTimeout(() => {
    if (dialogMode.value === 'add') {
      const newDataSet: DataSet = {
        ...dialogForm.value as DataSet,
        id: Date.now().toString(),
        createTime: new Date().toLocaleString(),
        createUser: '当前用户',
        dataSize: '0MB',
        recordCount: 0,
        lastUpdateTime: new Date().toLocaleString()
      }
      dataSetList.value.unshift(newDataSet)
      ElMessage.success('新增成功')
    } else if (dialogMode.value === 'edit') {
      const index = dataSetList.value.findIndex(item => item.id === currentRow.value?.id)
      if (index !== -1) {
        dataSetList.value[index] = {
          ...dataSetList.value[index],
          ...dialogForm.value,
          lastUpdateTime: new Date().toLocaleString()
        }
        ElMessage.success('编辑成功')
      }
    }

    saveDataToCache()
    showDialogForm.value = false
    loading.value = false
  }, 1000)
}

// 组件挂载
onMounted(() => {
  loadDataFromCache()
})
</script>

<style scoped>
.data-set-tab {
  height: 100%;
}

.top-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
