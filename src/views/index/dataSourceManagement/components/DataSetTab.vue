<!-- 数据集Tab组件 -->
<template>
  <div class="data-set-tab">
    <Block title="数据集管理" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <div class="top-buttons">
          <el-button size="small" type="primary" @click="onClickAdd">新增</el-button>
          <el-button size="small" type="primary" @click="onClickBatchDelete">批量删除</el-button>
          <el-button size="small" type="primary" @click="onClickBatchExport">批量导出</el-button>
          <el-button size="small" type="primary" @click="onClickBatchImport">批量导入</el-button>
          <el-button size="small" type="primary" @click="onClickUpdateLog">更新日志</el-button>
          
          <el-dropdown @command="handleMoreCommand">
            <el-button size="small" type="primary">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="dataSetTypeConfig">数据集类型配置</el-dropdown-item>
                <el-dropdown-item command="dataSetCategory">数据集分类</el-dropdown-item>
                <el-dropdown-item command="qualityMonitoringAssessment">质量监控与评估</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>

      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <div class="search-row">
            <div class="search-fields">
              <el-form :model="searchForm" inline>
                <el-form-item label="数据集名称" label-width="100px">
                  <el-input v-model="searchForm.name" placeholder="请输入名称" size="small" style="width: 160px" clearable />
                </el-form-item>
                <el-form-item label="数据源名称" label-width="100px">
                  <el-input v-model="searchForm.dataSourceName" placeholder="请输入数据源名称" size="small" style="width: 160px" clearable />
                </el-form-item>
                <el-form-item label="数据源类型" label-width="100px">
                  <el-select v-model="searchForm.dataSourceType" placeholder="请选择数据源类型" size="small" style="width: 160px" clearable>
                    <el-option label="MySql" value="MySql" />
                    <el-option label="Oracle" value="Oracle" />
                    <el-option label="SQL Server" value="SQL Server" />
                    <el-option label="达梦" value="达梦" />
                    <el-option label="Hive" value="Hive" />
                    <el-option label="MangoDB" value="MangoDB" />
                    <el-option label="Huawei GaussDB" value="Huawei GaussDB" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            <div class="search-buttons">
              <el-button size="small" type="primary" @click="onSearch">查询</el-button>
              <el-button size="small" @click="onReset">重置</el-button>
              <el-button size="small" @click="$router.back()">返回</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <div style="width: 100%;">
        <BaseTableComp
          :data="paginatedData"
          :columns="tableColumns"
          :buttons="tableButtons"
          :pagination="pagination"
          :height="tableHeight"
          @page-change="onPageChange"
          @size-change="onSizeChange"
          @selection-change="onSelectionChange"
          @button-click="onTableButtonClick"
        />
      </div>
    </Block>

    <!-- 新增/编辑/详情弹窗 -->
    <Dialog
      width="35%"
      v-model="showDialogForm"
      :title="dialogMode === 'add' ? '新增数据集' : dialogMode === 'edit' ? '编辑数据集' : '数据集详情'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="currentRow = null; dialogForm = {}"
      @click-confirm="onDialogConfirm"
    >
      <!-- 基础信息表单 -->
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
        :disabled="dialogMode === 'view'"
      />
    </Dialog>



    <!-- 数据集类型配置弹窗 -->
    <DataSetTypeConfigDialog
      v-model="showDataSetTypeConfigDialog"
    />

    <!-- 数据集分类弹窗 -->
    <DataSetCategoryDialog
      v-model="showDataSetCategoryDialog"
    />

    <!-- 质量监控与评估弹窗 -->
    <QualityMonitoringAssessmentDialog
      v-model="showQualityMonitoringAssessmentDialog"
    />
  </div>
</template>

<script setup lang="ts" name="DataSetTab">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

// 导入弹窗组件
import DataSetTypeConfigDialog from './DataSetTypeConfigDialog.vue'
import DataSetCategoryDialog from './DataSetCategoryDialog.vue'
import QualityMonitoringAssessmentDialog from './QualityMonitoringAssessmentDialog.vue'

// 数据集类型定义
interface DataSet {
  id: string
  name: string
  description: string
  type: string
  category: string
  status: boolean
  createTime: string
  createUser: string
  dataSize: string
  recordCount: number
  lastUpdateTime: string
  dataSourceName: string
  dataSourceType: string
}

// 响应式数据
const loading = ref(false)
const tableHeight = ref(400)
const currentRow = ref<DataSet | null>(null)
const selectedRows = ref<DataSet[]>([])

// 搜索表单数据

const searchForm = ref({
  name: '',
  dataSourceName: '',
  dataSourceType: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格列配置
const tableColumns = ref([
  { prop: 'name', label: '数据集名称', minWidth: 150, showOverflowTooltip: true },
  { prop: 'description', label: '描述', minWidth: 200, showOverflowTooltip: true },
  { prop: 'dataSourceName', label: '数据源名称', minWidth: 150, showOverflowTooltip: true },
  { prop: 'dataSourceType', label: '数据源类型', width: 120 },
  { prop: 'type', label: '数据集类型', width: 120 },
  { prop: 'category', label: '分类', width: 100 },
  { prop: 'dataSize', label: '数据大小', width: 100 },
  { prop: 'recordCount', label: '记录数', width: 100 },
  { prop: 'status', label: '状态', width: 80, type: 'switch' },
  { prop: 'createTime', label: '创建时间', width: 150 },
  { prop: 'createUser', label: '创建人', width: 100 },
  { prop: 'lastUpdateTime', label: '最后更新时间', width: 150 }
])

// 表格操作按钮
const tableButtons = ref([
  { label: '查看', type: 'primary', size: 'small', command: 'view' },
  { label: '编辑', type: 'primary', size: 'small', command: 'edit' },
  { label: '删除', type: 'danger', size: 'small', command: 'delete' }
])

// 弹窗相关
const showDialogForm = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const dialogForm = ref<Partial<DataSet>>({})

// 弹窗表单配置
const dialogFormProps = computed(() => [
  { label: '数据集名称', prop: 'name', type: 'text', required: true },
  { label: '描述', prop: 'description', type: 'textarea', required: true },
  { label: '数据集类型', prop: 'type', type: 'select', required: true, options: [
    { label: '结构化数据', value: 'structured' },
    { label: '半结构化数据', value: 'semi-structured' },
    { label: '非结构化数据', value: 'unstructured' }
  ]},
  { label: '数据集分类', prop: 'category', type: 'select', required: true, options: [
    { label: '业务数据', value: 'business' },
    { label: '日志数据', value: 'log' },
    { label: '监控数据', value: 'monitor' }
  ]},
  { label: '状态', prop: 'status', type: 'switch', required: true }
])

// 表单验证规则
const dialogFormRules = {
  name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据集类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择数据集分类', trigger: 'change' }]
}

// 各种弹窗显示状态
const showDataSetTypeConfigDialog = ref(false)
const showDataSetCategoryDialog = ref(false)
const showQualityMonitoringAssessmentDialog = ref(false)

// 缓存键
const STORAGE_KEY = 'dataSetManagement_data'

// 模拟数据
const dataSetList = ref<DataSet[]>([])

// 初始化模拟数据
const initMockData = () => {
  const mockData: DataSet[] = [
    {
      id: '1',
      name: '用户行为数据集',
      description: '用户在平台上的行为数据，包括点击、浏览、购买等',
      type: 'structured',
      category: 'business',
      status: true,
      createTime: '2024-01-15 10:30:00',
      createUser: '张三',
      dataSize: '2.5GB',
      recordCount: 1250000,
      lastUpdateTime: '2024-01-20 14:20:00',
      dataSourceName: 'MySQL用户数据库',
      dataSourceType: 'MySql'
    },
    {
      id: '2',
      name: '系统日志数据集',
      description: '系统运行过程中产生的日志信息',
      type: 'semi-structured',
      category: 'log',
      status: true,
      createTime: '2024-01-14 09:15:00',
      createUser: '李四',
      dataSize: '5.2GB',
      recordCount: 3200000,
      lastUpdateTime: '2024-01-21 16:45:00',
      dataSourceName: 'Oracle日志数据库',
      dataSourceType: 'Oracle'
    },
    {
      id: '3',
      name: '监控指标数据集',
      description: '系统性能监控指标数据',
      type: 'structured',
      category: 'monitor',
      status: false,
      createTime: '2024-01-13 15:20:00',
      createUser: '王五',
      dataSize: '1.8GB',
      recordCount: 890000,
      lastUpdateTime: '2024-01-19 11:30:00',
      dataSourceName: 'SQL Server监控库',
      dataSourceType: 'SQL Server'
    },
    {
      id: '4',
      name: '交易数据集',
      description: '电商平台交易相关数据',
      type: 'structured',
      category: 'business',
      status: true,
      createTime: '2024-01-12 16:45:00',
      createUser: '赵六',
      dataSize: '3.8GB',
      recordCount: 2100000,
      lastUpdateTime: '2024-01-22 09:30:00',
      dataSourceName: '达梦交易数据库',
      dataSourceType: '达梦'
    },
    {
      id: '5',
      name: '设备监控数据集',
      description: 'IoT设备监控数据收集',
      type: 'unstructured',
      category: 'monitor',
      status: true,
      createTime: '2024-01-11 11:20:00',
      createUser: '孙七',
      dataSize: '4.2GB',
      recordCount: 5600000,
      lastUpdateTime: '2024-01-23 15:10:00',
      dataSourceName: 'MongoDB设备数据库',
      dataSourceType: 'MangoDB'
    },
    {
      id: '6',
      name: '大数据分析集',
      description: 'Hive大数据仓库分析数据',
      type: 'structured',
      category: 'business',
      status: true,
      createTime: '2024-01-10 14:30:00',
      createUser: '周八',
      dataSize: '12.5GB',
      recordCount: 8900000,
      lastUpdateTime: '2024-01-24 10:15:00',
      dataSourceName: 'Hive数据仓库',
      dataSourceType: 'Hive'
    },
    {
      id: '7',
      name: '华为云数据集',
      description: '华为GaussDB云数据库数据',
      type: 'structured',
      category: 'business',
      status: true,
      createTime: '2024-01-09 09:45:00',
      createUser: '吴九',
      dataSize: '6.8GB',
      recordCount: 4200000,
      lastUpdateTime: '2024-01-25 16:20:00',
      dataSourceName: '华为云GaussDB',
      dataSourceType: 'Huawei GaussDB'
    }
  ]
  
  dataSetList.value = mockData
  saveDataToCache()
}

// 缓存操作
const loadDataFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      dataSetList.value = JSON.parse(cached)
    } else {
      initMockData()
    }
  } catch (error) {
    console.error('加载数据集数据失败:', error)
    initMockData()
  }
}

const saveDataToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataSetList.value))
  } catch (error) {
    console.error('保存数据集数据失败:', error)
  }
}

// 计算属性
const filteredData = computed(() => {
  let filtered = dataSetList.value

  if (searchForm.value.name) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }

  if (searchForm.value.dataSourceName) {
    filtered = filtered.filter(item =>
      item.dataSourceName.toLowerCase().includes(searchForm.value.dataSourceName.toLowerCase())
    )
  }

  if (searchForm.value.dataSourceType) {
    filtered = filtered.filter(item => item.dataSourceType === searchForm.value.dataSourceType)
  }

  pagination.total = filtered.length
  return filtered
})

const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 事件处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 200
}

const onSearch = () => {
  pagination.page = 1
}

const onReset = () => {
  searchForm.value = {
    name: '',
    dataSourceName: '',
    dataSourceType: ''
  }
  pagination.page = 1
}

const onPageChange = (page: number) => {
  pagination.page = page
}

const onSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

const onSelectionChange = (selection: DataSet[]) => {
  selectedRows.value = selection
}

// 顶部按钮事件
const onClickAdd = () => {
  dialogMode.value = 'add'
  dialogForm.value = {
    status: true
  }
  showDialogForm.value = true
}

const onClickBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条数据吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const selectedIds = selectedRows.value.map(row => row.id)
    dataSetList.value = dataSetList.value.filter(item => !selectedIds.includes(item.id))
    saveDataToCache()
    ElMessage.success('删除成功')
  })
}

const onClickBatchExport = () => {
  ElMessage.info('批量导出功能开发中...')
}

const onClickBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

const onClickUpdateLog = () => {
  ElMessage.info('更新日志功能开发中...')
}



// 更多操作下拉菜单
const handleMoreCommand = (command: string) => {
  switch (command) {
    case 'dataSetTypeConfig':
      showDataSetTypeConfigDialog.value = true
      break
    case 'dataSetCategory':
      showDataSetCategoryDialog.value = true
      break
    case 'qualityMonitoringAssessment':
      showQualityMonitoringAssessmentDialog.value = true
      break
  }
}

// 表格按钮点击事件
const onTableButtonClick = (command: string, row: DataSet) => {
  currentRow.value = row

  switch (command) {
    case 'view':
      dialogMode.value = 'view'
      dialogForm.value = { ...row }
      showDialogForm.value = true
      break
    case 'edit':
      dialogMode.value = 'edit'
      dialogForm.value = { ...row }
      showDialogForm.value = true
      break
    case 'delete':
      ElMessageBox.confirm(
        `确定要删除数据集"${row.name}"吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        const index = dataSetList.value.findIndex(item => item.id === row.id)
        if (index !== -1) {
          dataSetList.value.splice(index, 1)
          saveDataToCache()
          ElMessage.success('删除成功')
        }
      })
      break
  }
}

// 弹窗确认
const onDialogConfirm = () => {
  // 表单验证逻辑
  loading.value = true

  setTimeout(() => {
    if (dialogMode.value === 'add') {
      const newDataSet: DataSet = {
        ...dialogForm.value as DataSet,
        id: Date.now().toString(),
        createTime: new Date().toLocaleString(),
        createUser: '当前用户',
        dataSize: '0MB',
        recordCount: 0,
        lastUpdateTime: new Date().toLocaleString()
      }
      dataSetList.value.unshift(newDataSet)
      ElMessage.success('新增成功')
    } else if (dialogMode.value === 'edit') {
      const index = dataSetList.value.findIndex(item => item.id === currentRow.value?.id)
      if (index !== -1) {
        dataSetList.value[index] = {
          ...dataSetList.value[index],
          ...dialogForm.value,
          lastUpdateTime: new Date().toLocaleString()
        }
        ElMessage.success('编辑成功')
      }
    }

    saveDataToCache()
    showDialogForm.value = false
    loading.value = false
  }, 1000)
}

// 组件挂载
onMounted(() => {
  loadDataFromCache()
})
</script>

<style scoped>
.data-set-tab {
  height: 100%;
}

.top-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search {
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-fields {
  flex: 1;
}

.search-buttons {
  display: flex;
  gap: 8px;
  margin-left: 20px;
}
</style>
