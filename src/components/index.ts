import {App} from 'vue'
import TopComp from './common/top-comp.vue'
import LabelsComp from './common/labels-comp.vue'
import BaseTableComp from './common/basetable-comp.vue'
import TableColumnComp from './common/tablecolumn-comp.vue'
import DrawerComp from './common/drawer-comp.vue'
import DialogComp from './common/dialog-comp.vue'
import UploadFileComp from './common/uploadfile-comp.vue'
import ExcelComp from './common/excel-comp.vue'
import XlsxPlusComp from './common/xlsx-plus-comp.vue'
import ExportComp from './common/export-comp.vue'
import DrawerPlusComp from './common/drawer-plus-comp.vue'
import ProgressBar from './common/progress-bar.vue'
import FormComp from './common/form-comp.vue'
import TreeComp from './common/tree_comp.vue'
import InfoOverViewComp from './common/info-overview-comp.vue'
import LinkedDataComp from './common/linked-data-comp.vue'
import AuthorizedComp from './common/authorized-comp.vue'
import DropdownTree from './common/dropdownTree.vue'
import TimePicker from './common/TimePicker.vue'
import ViewFlow from './common/ViewFlow.vue'
import DepartmentSelection from './DepartmentSelection.vue'
import BusinessProcess from './BusinessProcess.vue'
import AI from './AI.vue'
import ValidateCode from './ValidateCode.vue'

import Organize from './organize.vue'
import LedgerFillConfig from './ledger-fill-config.vue'
import scheduleProgressComp from './common/schedule-progress-comp.vue'
// 共用组件全局注册, 非共用单页引用
const comps: any = [
	{name: 'TopComp', component: TopComp},
	{name: 'LabelsComp', component: LabelsComp},
	{name: 'BaseTableComp', component: BaseTableComp},
	{name: 'TableColumnComp', component: TableColumnComp},
	{name: 'DrawerComp', component: DrawerComp},
	{name: 'DialogComp', component: DialogComp},
	{name: 'UploadFileComp', component: UploadFileComp},
	{name: 'FormComp', component: FormComp},
	{name: 'ExcelComp', component: ExcelComp},
	{name: 'XlsxPlusComp', component: XlsxPlusComp},
	{name: 'ExportComp', component: ExportComp},
	{name: 'DrawerPlusComp', component: DrawerPlusComp},
	{name: 'ProgressBar', component: ProgressBar},
	{name: 'TreeComp', component: TreeComp},
	{name: 'InfoOverViewComp', component: InfoOverViewComp},
	{name: 'LinkedDataComp', component: LinkedDataComp},
	{name: 'AuthorizedComp', component: AuthorizedComp},
	{name: 'DropdownTree', component: DropdownTree},
	{name: 'Organize', component: Organize},
	{name: 'LedgerFillConfig', component: LedgerFillConfig},
	{name: 'scheduleProgressComp', component: scheduleProgressComp},
	{name: 'TimePicker', component: TimePicker},
	{name: 'ViewFlow', component: ViewFlow},
	{name: 'DepartmentSelection', component: DepartmentSelection},
	{name: 'BusinessProcess', component: BusinessProcess},
	{name: 'AI', component: AI},
	{name: 'ValidateCode', component: ValidateCode},
]

export const components = (app: App<Element>) => {
	if (app) {
		comps.forEach((comp: any) => app.component(comp.name, comp.component))
	}
}
